#include "Performance/OptimizedTieredObjectPool.h"
#include "HAL/PlatformTime.h"
#include "LevelGen/MapUtil.h"

/**
 * 【优化的分层对象池】实现文件
 * 
 * 提供FOptimizedTieredObjectPool的显式模板实例化和特化实现。
 * 确保常用类型的编译时优化和链接正确性。
 */

// 【显式模板实例化】为常用类型提供显式实例化
template class FOptimizedTieredObjectPool<FMapCell>;

// 【特化实现】为FMapCell提供优化的压缩/解压实现
template<>
TArray<uint8> FOptimizedTieredObjectPool<FMapCell>::CompressObject(const FMapCell& Object) const
{
    // 【FMapCell特化】针对FMapCell的优化压缩实现
    TArray<uint8> RawData;
    
    // 【高效序列化】直接序列化FMapCell的关键字段
    FMemoryWriter Writer(RawData);
    
    // 序列化FMapCell的主要字段
    Writer << const_cast<FMapCell&>(Object).CellType;
    Writer << const_cast<FMapCell&>(Object).Height;
    Writer << const_cast<FMapCell&>(Object).Temperature;
    Writer << const_cast<FMapCell&>(Object).Humidity;
    Writer << const_cast<FMapCell&>(Object).Position;
    
    // 【智能压缩】只有数据足够大时才压缩
    if (RawData.Num() > 64) // 小于64字节不压缩
    {
        TArray<uint8> CompressedData;
        const int32 MaxCompressedSize = FCompression::CompressMemoryBound(NAME_Zlib, RawData.Num());
        CompressedData.SetNumUninitialized(MaxCompressedSize);
        
        int32 CompressedSize = MaxCompressedSize;
        const bool bSuccess = FCompression::CompressMemory(
            NAME_Zlib,
            CompressedData.GetData(),
            CompressedSize,
            RawData.GetData(),
            RawData.Num()
        );
        
        // 【压缩效果检查】只有压缩效果显著时才使用压缩数据
        if (bSuccess && CompressedSize < RawData.Num() * 0.8f) // 压缩率超过20%才使用
        {
            CompressedData.SetNum(CompressedSize);
            return CompressedData;
        }
    }
    
    // 【直接返回】压缩无效果时返回原始数据
    return RawData;
}

template<>
TSharedPtr<FMapCell, ESPMode::ThreadSafe> FOptimizedTieredObjectPool<FMapCell>::DecompressObject(const TArray<uint8>& CompressedData) const
{
    if (CompressedData.Num() == 0)
    {
        return MakeShared<FMapCell>();
    }

    // 【智能解压】尝试解压，失败则当作原始数据处理
    TArray<uint8> RawData;
    
    // 【解压尝试】先尝试解压缩
    if (CompressedData.Num() > 4) // 压缩数据至少4字节
    {
        int32 UncompressedSize = 0;
        
        // 【获取解压大小】
        if (FCompression::UncompressMemory(
            NAME_Zlib,
            nullptr,
            UncompressedSize,
            CompressedData.GetData(),
            CompressedData.Num(),
            COMPRESS_NoFlags,
            0))
        {
            RawData.SetNumUninitialized(UncompressedSize);
            
            // 【执行解压】
            if (FCompression::UncompressMemory(
                NAME_Zlib,
                RawData.GetData(),
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num()))
            {
                // 解压成功，继续反序列化
            }
            else
            {
                // 解压失败，当作原始数据
                RawData = CompressedData;
            }
        }
        else
        {
            // 不是压缩数据，直接使用
            RawData = CompressedData;
        }
    }
    else
    {
        RawData = CompressedData;
    }

    // 【反序列化】从字节数组恢复FMapCell对象
    if (RawData.Num() >= sizeof(int32) * 4 + sizeof(FIntPoint)) // 最小数据大小检查
    {
        TSharedPtr<FMapCell, ESPMode::ThreadSafe> Object = MakeShared<FMapCell>();
        
        FMemoryReader Reader(RawData);
        
        try
        {
            // 【安全反序列化】按序列化顺序读取字段
            Reader << Object->CellType;
            Reader << Object->Height;
            Reader << Object->Temperature;
            Reader << Object->Humidity;
            Reader << Object->Position;
            
            return Object;
        }
        catch (...)
        {
            // 反序列化失败，返回默认对象
            UE_LOG(LogTemp, Warning, TEXT("【分层对象池】FMapCell反序列化失败，返回默认对象"));
        }
    }
    
    // 【失败回退】创建默认FMapCell对象
    return MakeShared<FMapCell>();
}

// 【性能测试函数】用于验证分层对象池的性能
namespace TieredObjectPoolTest
{
    /**
     * 【性能基准测试】测试分层对象池的性能表现
     */
    void BenchmarkTieredObjectPool(int32 PoolSize = 1000, int32 TestIterations = 10000)
    {
        UE_LOG(LogTemp, Log, TEXT("【分层对象池测试】开始性能基准测试，池大小: %d，迭代次数: %d"), PoolSize, TestIterations);
        
        FOptimizedTieredObjectPool<FMapCell> TestPool(PoolSize / 3, PoolSize / 3, PoolSize / 3);
        
        // 【预热阶段】预热池以获得稳定的测试结果
        TArray<TSharedPtr<FMapCell, ESPMode::ThreadSafe>> PrewarmObjects;
        PrewarmObjects.Reserve(PoolSize / 2);
        
        for (int32 i = 0; i < PoolSize / 2; ++i)
        {
            PrewarmObjects.Add(TestPool.GetObject());
        }
        
        for (auto& Object : PrewarmObjects)
        {
            TestPool.ReturnObject(Object, FOptimizedTieredObjectPool<FMapCell>::ETier::Hot);
        }
        
        PrewarmObjects.Empty();
        
        // 【性能测试阶段】
        const double StartTime = FPlatformTime::Seconds();
        
        TArray<TSharedPtr<FMapCell, ESPMode::ThreadSafe>> TestObjects;
        TestObjects.Reserve(TestIterations);
        
        // 【获取测试】
        for (int32 i = 0; i < TestIterations; ++i)
        {
            TestObjects.Add(TestPool.GetObject());
        }
        
        const double GetTime = FPlatformTime::Seconds();
        
        // 【归还测试】
        for (int32 i = 0; i < TestIterations; ++i)
        {
            FOptimizedTieredObjectPool<FMapCell>::ETier Tier = 
                (i % 3 == 0) ? FOptimizedTieredObjectPool<FMapCell>::ETier::Hot :
                (i % 3 == 1) ? FOptimizedTieredObjectPool<FMapCell>::ETier::Warm :
                               FOptimizedTieredObjectPool<FMapCell>::ETier::Cold;
            
            TestPool.ReturnObject(TestObjects[i], Tier);
        }
        
        const double ReturnTime = FPlatformTime::Seconds();
        
        // 【统计结果】
        int32 HotCount, WarmCount, ColdCount;
        TestPool.GetPoolSizes(HotCount, WarmCount, ColdCount);
        
        const float HotHitRate = TestPool.GetTierHitRate(FOptimizedTieredObjectPool<FMapCell>::ETier::Hot);
        const float WarmHitRate = TestPool.GetTierHitRate(FOptimizedTieredObjectPool<FMapCell>::ETier::Warm);
        const float ColdHitRate = TestPool.GetTierHitRate(FOptimizedTieredObjectPool<FMapCell>::ETier::Cold);
        const float TotalHitRate = TestPool.GetTotalHitRate();
        
        // 【结果输出】
        UE_LOG(LogTemp, Log, TEXT("【分层对象池测试】性能测试完成"));
        UE_LOG(LogTemp, Log, TEXT("  获取时间: %.3f ms (%.1f ops/ms)"), 
               (GetTime - StartTime) * 1000.0, TestIterations / ((GetTime - StartTime) * 1000.0));
        UE_LOG(LogTemp, Log, TEXT("  归还时间: %.3f ms (%.1f ops/ms)"), 
               (ReturnTime - GetTime) * 1000.0, TestIterations / ((ReturnTime - GetTime) * 1000.0));
        UE_LOG(LogTemp, Log, TEXT("  总时间: %.3f ms"), (ReturnTime - StartTime) * 1000.0);
        UE_LOG(LogTemp, Log, TEXT("  池大小 - 热: %d, 温: %d, 冷: %d"), HotCount, WarmCount, ColdCount);
        UE_LOG(LogTemp, Log, TEXT("  命中率 - 热: %.1f%%, 温: %.1f%%, 冷: %.1f%%, 总: %.1f%%"), 
               HotHitRate * 100.0f, WarmHitRate * 100.0f, ColdHitRate * 100.0f, TotalHitRate * 100.0f);
    }

    /**
     * 【压缩测试】测试冷池的压缩效果
     */
    void TestCompressionEfficiency()
    {
        UE_LOG(LogTemp, Log, TEXT("【分层对象池测试】开始压缩效率测试"));
        
        FOptimizedTieredObjectPool<FMapCell> TestPool(10, 10, 100);
        
        // 【创建测试数据】
        TArray<TSharedPtr<FMapCell, ESPMode::ThreadSafe>> TestObjects;
        for (int32 i = 0; i < 50; ++i)
        {
            auto Object = MakeShared<FMapCell>();
            Object->CellType = static_cast<ECellType>(i % 4);
            Object->Height = i * 10.0f;
            Object->Temperature = 20.0f + (i % 30);
            Object->Humidity = 0.5f + (i % 50) * 0.01f;
            Object->Position = FIntPoint(i % 100, i / 100);
            
            TestObjects.Add(Object);
        }
        
        // 【压缩存储测试】将所有对象存储到冷池
        for (auto& Object : TestObjects)
        {
            TestPool.ReturnObject(Object, FOptimizedTieredObjectPool<FMapCell>::ETier::Cold);
        }
        
        // 【解压获取测试】从冷池获取所有对象
        TArray<TSharedPtr<FMapCell, ESPMode::ThreadSafe>> RetrievedObjects;
        for (int32 i = 0; i < 50; ++i)
        {
            RetrievedObjects.Add(TestPool.GetObject());
        }
        
        // 【验证数据完整性】
        int32 CorrectObjects = 0;
        for (int32 i = 0; i < FMath::Min(TestObjects.Num(), RetrievedObjects.Num()); ++i)
        {
            const auto& Original = TestObjects[i];
            const auto& Retrieved = RetrievedObjects[i];
            
            if (Retrieved.IsValid() && 
                Retrieved->CellType == Original->CellType &&
                FMath::IsNearlyEqual(Retrieved->Height, Original->Height) &&
                FMath::IsNearlyEqual(Retrieved->Temperature, Original->Temperature))
            {
                CorrectObjects++;
            }
        }
        
        const float DataIntegrity = static_cast<float>(CorrectObjects) / TestObjects.Num() * 100.0f;
        const float ColdHitRate = TestPool.GetTierHitRate(FOptimizedTieredObjectPool<FMapCell>::ETier::Cold);
        
        UE_LOG(LogTemp, Log, TEXT("【分层对象池测试】压缩测试完成"));
        UE_LOG(LogTemp, Log, TEXT("  数据完整性: %.1f%% (%d/%d)"), DataIntegrity, CorrectObjects, TestObjects.Num());
        UE_LOG(LogTemp, Log, TEXT("  冷池命中率: %.1f%%"), ColdHitRate * 100.0f);
    }
}

// 【自动测试】在模块加载时自动运行测试（仅在开发构建中）
#if !UE_BUILD_SHIPPING
static struct FTieredObjectPoolAutoTest
{
    FTieredObjectPoolAutoTest()
    {
        // 【延迟测试】在引擎完全初始化后运行测试
        FTSTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateLambda([](float DeltaTime) -> bool
        {
            static bool bTestRun = false;
            if (!bTestRun)
            {
                bTestRun = true;
                
                UE_LOG(LogTemp, Log, TEXT("【分层对象池】开始自动测试"));
                
                // 运行性能基准测试
                TieredObjectPoolTest::BenchmarkTieredObjectPool(300, 1000);
                
                // 运行压缩效率测试
                TieredObjectPoolTest::TestCompressionEfficiency();
                
                UE_LOG(LogTemp, Log, TEXT("【分层对象池】自动测试完成"));
            }
            return false; // 只运行一次
        }), 2.0f); // 2秒后运行
    }
} GAutoTest;
#endif
