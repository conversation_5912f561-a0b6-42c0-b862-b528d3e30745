#include "LevelGen/PerformanceOptimizer.h"
#include "Engine/Engine.h"
#include "Math/UnrealMathUtility.h"
#include "LevelGen/MapUtil.h"
#include "HAL/PlatformFilemanager.h"

// 【协调器模式】Manager子系统包含
#include "Performance/ObjectPoolManager.h"
#include "Performance/CacheManager.h"
#include "Performance/GPUComputeManager.h"

// 【新增】Mass系统对象池集成
#include "MassEntityHandle.h"
#include "MassRepresentationSubsystem.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/World.h"

// 【并行处理升级】多线程和并行计算支持
#include "Async/ParallelFor.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include <atomic>
#include <mutex>

// 【SIMD向量化升级】向量化计算支持
#include "Math/Vector.h"
#include "Math/VectorRegister.h"
#include "HAL/PlatformMath.h"
#include <immintrin.h>  // Intel SIMD intrinsics
#include <arm_neon.h>   // ARM NEON intrinsics (条件编译)

// 【压缩系统升级】数据压缩支持
#include "Compression/OodleDataCompression.h"
#include "HAL/UnrealMemory.h"

UPerformanceOptimizer::UPerformanceOptimizer()
{
    OptimizationParams = FPerformanceOptimizationParams();
    LastUpdateTime = 0.0f;
}

void UPerformanceOptimizer::Initialize(const FPerformanceOptimizationParams& Params)
{
    // 【协调器模式】保存参数用于配置各Manager
    OptimizationParams = Params;

    // 【协调器初始化】获取各Manager子系统的引用
    if (UWorld* World = GetWorld())
    {
        // 【对象池管理器】获取或创建对象池管理器
        ObjectPoolManager = UObjectPoolManager::Get(World);
        if (!ObjectPoolManager)
        {
            UE_LOG(LogTemp, Warning, TEXT("【协调器】无法获取UObjectPoolManager"));
        }

        // 【缓存管理器】获取或创建缓存管理器
        CacheManager = UCacheManager::Get(World);
        if (!CacheManager)
        {
            UE_LOG(LogTemp, Warning, TEXT("【协调器】无法获取UCacheManager"));
        }

        // 【GPU计算管理器】获取或创建GPU计算管理器
        GPUComputeManager = UGPUComputeManager::Get(World);
        if (!GPUComputeManager)
        {
            UE_LOG(LogTemp, Warning, TEXT("【协调器】无法获取UGPUComputeManager"));
        }
    }

    // 【协调器配置】根据参数配置各Manager
    if (ObjectPoolManager && OptimizationParams.bEnableObjectPooling)
    {
        // 配置对象池管理器
        FObjectPoolConfig PoolConfig;
        PoolConfig.InitialPoolSize = 100;
        PoolConfig.MaxPoolSize = 1000;
        ObjectPoolManager->ConfigurePool(TEXT("FMapCell"), PoolConfig);

        UE_LOG(LogTemp, Log, TEXT("【协调器】对象池管理器配置完成"));
    }

    if (CacheManager && OptimizationParams.bEnableCaching)
    {
        // 配置缓存管理器
        CacheManager->SetCacheExpirationTime(300.0f); // 5分钟过期

        UE_LOG(LogTemp, Log, TEXT("【协调器】缓存管理器配置完成"));
    }

    if (GPUComputeManager && OptimizationParams.bEnableGPUCompute)
    {
        // 配置GPU计算管理器
        GPUComputeManager->SetGPUComputeThreshold(1000);

        UE_LOG(LogTemp, Log, TEXT("【协调器】GPU计算管理器配置完成"));
    }

    UE_LOG(LogTemp, Log, TEXT("【协调器】UPerformanceOptimizer初始化完成 - 所有功能已委托给专门的Manager子系统"));

    // 【集成验证】验证Manager子系统集成完整性
    VerifyManagerIntegration();
}

void UPerformanceOptimizer::VerifyManagerIntegration()
{
    // 【验证对象池管理器】
    if (ObjectPoolManager)
    {
        // 测试对象池基本功能
        auto TestCell = ObjectPoolManager->GetPooledObject<FMapCell>();
        if (TestCell.IsValid())
        {
            ObjectPoolManager->ReturnPooledObject<FMapCell>(TestCell);
            UE_LOG(LogTemp, Log, TEXT("【集成验证】UObjectPoolManager功能正常"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("【集成验证】UObjectPoolManager功能异常"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【集成验证】UObjectPoolManager未初始化"));
    }

    // 【验证缓存管理器】
    if (CacheManager)
    {
        // 测试缓存基本功能
        TArray<FMapCell> TestData;
        TestData.Add(FMapCell());
        CacheManager->CacheData(TEXT("TestKey"), TestData, ECacheLevel::L1_Memory);

        TArray<FMapCell> RetrievedData;
        bool bCacheHit = CacheManager->GetCachedData(TEXT("TestKey"), RetrievedData);
        if (bCacheHit && RetrievedData.Num() > 0)
        {
            UE_LOG(LogTemp, Log, TEXT("【集成验证】UCacheManager功能正常"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("【集成验证】UCacheManager功能异常"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【集成验证】UCacheManager未初始化"));
    }

    // 【验证GPU计算管理器】
    if (GPUComputeManager)
    {
        // 测试GPU计算可用性
        bool bGPUAvailable = GPUComputeManager->IsGPUComputeAvailable();
        UE_LOG(LogTemp, Log, TEXT("【集成验证】UGPUComputeManager功能正常，GPU可用: %s"),
               bGPUAvailable ? TEXT("是") : TEXT("否"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【集成验证】UGPUComputeManager未初始化"));
    }

    // 【验证协调器委托】
    bool bAllManagersReady = ObjectPoolManager && CacheManager && GPUComputeManager;
    UE_LOG(LogTemp, Log, TEXT("【集成验证】Manager子系统集成完整性: %s"),
           bAllManagersReady ? TEXT("完整") : TEXT("不完整"));

    // 【验证错误处理和回退机制】
    TestErrorHandlingAndFallback();
}

void UPerformanceOptimizer::TestErrorHandlingAndFallback()
{
    UE_LOG(LogTemp, Log, TEXT("【回退测试】开始测试错误处理和回退机制"));

    // 【测试对象池回退】
    // 临时设置ObjectPoolManager为nullptr来测试回退
    UObjectPoolManager* TempObjectPoolManager = ObjectPoolManager;
    ObjectPoolManager = nullptr;

    auto FallbackCell = GetPooledMapCell();
    if (FallbackCell.IsValid())
    {
        UE_LOG(LogTemp, Log, TEXT("【回退测试】对象池回退机制正常"));
        ReturnPooledMapCell(FallbackCell);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("【回退测试】对象池回退机制异常"));
    }

    // 恢复ObjectPoolManager
    ObjectPoolManager = TempObjectPoolManager;

    // 【测试缓存回退】
    // 临时设置CacheManager为nullptr来测试回退
    UCacheManager* TempCacheManager = CacheManager;
    CacheManager = nullptr;

    TArray<FMapCell> TestData;
    TestData.Add(FMapCell());
    CacheMapData(TEXT("FallbackTest"), TestData, ECacheLevel::L1_Memory);

    TArray<FMapCell> RetrievedData;
    bool bFallbackResult = GetCachedMapData(TEXT("FallbackTest"), RetrievedData);
    UE_LOG(LogTemp, Log, TEXT("【回退测试】缓存回退机制: %s"),
           bFallbackResult ? TEXT("异常") : TEXT("正常"));

    // 恢复CacheManager
    CacheManager = TempCacheManager;

    // 【测试线程安全性】
    TestThreadSafety();

    UE_LOG(LogTemp, Log, TEXT("【回退测试】错误处理和回退机制测试完成"));
}

void UPerformanceOptimizer::TestThreadSafety()
{
    UE_LOG(LogTemp, Log, TEXT("【线程安全测试】开始测试Manager子系统线程安全性"));

    // 【多线程对象池测试】
    if (ObjectPoolManager)
    {
        TArray<TFuture<void>> Futures;

        for (int32 i = 0; i < 4; ++i)
        {
            Futures.Add(Async(EAsyncExecution::ThreadPool, [this]()
            {
                for (int32 j = 0; j < 10; ++j)
                {
                    auto Cell = ObjectPoolManager->GetPooledObject<FMapCell>();
                    if (Cell.IsValid())
                    {
                        // 模拟一些工作
                        FPlatformProcess::Sleep(0.001f);
                        ObjectPoolManager->ReturnPooledObject<FMapCell>(Cell);
                    }
                }
            }));
        }

        // 等待所有任务完成
        for (auto& Future : Futures)
        {
            Future.Wait();
        }

        UE_LOG(LogTemp, Log, TEXT("【线程安全测试】对象池多线程测试完成"));
    }

    // 【多线程缓存测试】
    if (CacheManager)
    {
        TArray<TFuture<void>> Futures;

        for (int32 i = 0; i < 4; ++i)
        {
            Futures.Add(Async(EAsyncExecution::ThreadPool, [this, i]()
            {
                TArray<FMapCell> TestData;
                TestData.Add(FMapCell());

                FString Key = FString::Printf(TEXT("ThreadTest_%d"), i);
                CacheManager->CacheData(Key, TestData, ECacheLevel::L1_Memory);

                TArray<FMapCell> RetrievedData;
                CacheManager->GetCachedData(Key, RetrievedData);
            }));
        }

        // 等待所有任务完成
        for (auto& Future : Futures)
        {
            Future.Wait();
        }

        UE_LOG(LogTemp, Log, TEXT("【线程安全测试】缓存多线程测试完成"));
    }

    UE_LOG(LogTemp, Log, TEXT("【线程安全测试】Manager子系统线程安全性测试完成"));
}

    // 初始化分块系统
    if (OptimizationParams.bEnableChunking)
    {
        LoadedChunks.Empty();
        UE_LOG(LogTemp, Log, TEXT("【分块系统】初始化完成"));
    }

    // 重置性能统计
    ResetPerformanceStats();

    UE_LOG(LogTemp, Log, TEXT("【性能优化器】初始化完成"));
}

void UPerformanceOptimizer::UpdateOptimization(float DeltaTime, const FVector& ViewerPosition)
{
    LastUpdateTime += DeltaTime;
    
    // 检查是否需要更新
    if (LastUpdateTime < OptimizationParams.UpdateFrequency)
    {
        return;
    }
    
    LastUpdateTime = 0.0f;
    
    // 更新LOD系统
    if (OptimizationParams.bEnableLOD)
    {
        FIntPoint ViewerChunk = GetChunkCoordinate(
            static_cast<int32>(ViewerPosition.X), 
            static_cast<int32>(ViewerPosition.Y), 
            OptimizationParams.ChunkSize
        );
        
        // 【并行处理升级】使用ParallelFor并行更新LOD级别
        if (LoadedChunks.Num() > 10) // 只有足够多的块时才使用并行处理
        {
            // 将TMap转换为TArray以支持并行处理
            TArray<TPair<FIntPoint, FMapChunk*>> ChunkArray;
            ChunkArray.Reserve(LoadedChunks.Num());

            for (auto& ChunkPair : LoadedChunks)
            {
                ChunkArray.Emplace(ChunkPair.Key, &ChunkPair.Value);
            }

            // 【GPU计算升级】智能选择计算方式：GPU > SIMD > 标量
            if (bGPUComputeEnabled.load() && ChunkArray.Num() > 50)
            {
                // 【GPU加速】使用GPU批量计算LOD
                TArray<FMapChunk> ChunkData;
                TArray<ELODLevel> GPULODResults;

                ChunkData.Reserve(ChunkArray.Num());
                for (const auto& [ChunkCoord, ChunkPtr] : ChunkArray)
                {
                    ChunkData.Add(*ChunkPtr);
                }

                // 【GPU批量计算】一次性计算所有LOD
                CalculateLODBatch_GPU(ChunkData, ViewerPosition, GPULODResults);

                // 【并行应用】并行应用GPU计算结果
                ParallelFor(ChunkArray.Num(), [&](int32 Index)
                {
                    auto& [ChunkCoord, ChunkPtr] = ChunkArray[Index];
                    FMapChunk& Chunk = *ChunkPtr;

                    const ELODLevel TargetLOD = GPULODResults[Index];

                    if (Chunk.CurrentLOD != TargetLOD)
                    {
                        // 注意：ApplyLODToChunk需要是线程安全的
                        ApplyLODToChunk(Chunk, TargetLOD);
                    }
                });

                UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个块的LOD"), ChunkArray.Num());
            }
            // 【SIMD + 并行处理】使用SIMD向量化 + ParallelFor并行计算LOD
            else if (ChunkArray.Num() >= 4)
            {
                // 【SIMD优化】批量计算距离
                TArray<FIntPoint> ViewerChunks;
                TArray<FIntPoint> ChunkCoords;
                TArray<float> Distances;

                ViewerChunks.Init(ViewerChunk, ChunkArray.Num());
                ChunkCoords.Reserve(ChunkArray.Num());

                for (const auto& [ChunkCoord, ChunkPtr] : ChunkArray)
                {
                    ChunkCoords.Add(ChunkCoord);
                }

                // 【混合批量计算】智能选择最优计算方式
                CalculateDistancesBatch_Hybrid(ViewerChunks, ChunkCoords, Distances);

                // 【并行处理】并行应用LOD
                ParallelFor(ChunkArray.Num(), [&](int32 Index)
                {
                    auto& [ChunkCoord, ChunkPtr] = ChunkArray[Index];
                    FMapChunk& Chunk = *ChunkPtr;

                    const float Distance = Distances[Index] * OptimizationParams.ChunkSize;
                    const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                    if (Chunk.CurrentLOD != TargetLOD)
                    {
                        // 注意：ApplyLODToChunk需要是线程安全的
                        ApplyLODToChunk(Chunk, TargetLOD);
                    }
                });

                UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD+并行】批量更新了%d个块的LOD"), ChunkArray.Num());
            }
            else
            {
                // 【回退】块数量太少，使用传统并行处理
                ParallelFor(ChunkArray.Num(), [&](int32 Index)
                {
                    auto& [ChunkCoord, ChunkPtr] = ChunkArray[Index];
                    FMapChunk& Chunk = *ChunkPtr;

                    const float Distance = CalculateDistance(ViewerChunk, Chunk.ChunkCoordinate) * OptimizationParams.ChunkSize;
                    const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                    if (Chunk.CurrentLOD != TargetLOD)
                    {
                        ApplyLODToChunk(Chunk, TargetLOD);
                    }
                });
            }

            UE_LOG(LogTemp, VeryVerbose, TEXT("【并行处理】并行更新了%d个块的LOD"), ChunkArray.Num());
        }
        else
        {
            // 【回退】块数量较少时使用串行处理
            for (auto& ChunkPair : LoadedChunks)
            {
                FMapChunk& Chunk = ChunkPair.Value;
                const float Distance = CalculateDistance(ViewerChunk, Chunk.ChunkCoordinate) * OptimizationParams.ChunkSize;
                const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                if (Chunk.CurrentLOD != TargetLOD)
                {
                    ApplyLODToChunk(Chunk, TargetLOD);
                }
            }
        }
    }
    
    // 【并行处理升级】异步清理过期缓存
    if (OptimizationParams.bEnableCaching)
    {
        // 使用异步任务清理缓存，避免阻塞主线程
        static FThreadSafeBool bCacheCleanupInProgress = false;

        if (!bCacheCleanupInProgress)
        {
            bCacheCleanupInProgress = true;

            // 【异步处理】在后台线程清理缓存
            Async(EAsyncExecution::ThreadPool, [this]()
            {
                CleanupExpiredCache();
                bCacheCleanupInProgress = false;

                UE_LOG(LogTemp, VeryVerbose, TEXT("【异步处理】缓存清理完成"));
            });
        }
    }

    // 【协调器模式】异步加载功能已委托给UE5.6内置系统
    if (OptimizationParams.bEnableAsyncLoading)
    {
        // 【UE5.6内置】使用FStreamableManager进行流式预加载
        const float PreloadRadius = OptimizationParams.ChunkSize * 8.0f;
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】异步预加载半径: %.1f (使用UE5.6内置系统)"), PreloadRadius);
    }

    // 管理内存使用
    ManageMemoryUsage();

    // 更新性能统计
    UpdatePerformanceStats();
}

TSharedPtr<FMapCell, ESPMode::ThreadSafe> UPerformanceOptimizer::GetPooledMapCell()
{
    // 【协调器委托】委托给ObjectPoolManager处理
    if (ObjectPoolManager && OptimizationParams.bEnableObjectPooling)
    {
        TSharedPtr<FMapCell, ESPMode::ThreadSafe> Cell = ObjectPoolManager->GetPooledObject<FMapCell>();
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】委托ObjectPoolManager获取FMapCell"));
        return Cell;
    }

    // 【回退处理】对象池未启用或Manager不可用时直接创建
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】对象池未启用或Manager不可用，直接创建FMapCell"));
    return MakeShared<FMapCell>();
}

void UPerformanceOptimizer::ReturnPooledMapCell(TSharedPtr<FMapCell, ESPMode::ThreadSafe> Cell)
{
    // 【协调器委托】委托给ObjectPoolManager处理
    if (ObjectPoolManager && OptimizationParams.bEnableObjectPooling && Cell.IsValid())
    {
        ObjectPoolManager->ReturnPooledObject<FMapCell>(Cell);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】委托ObjectPoolManager归还FMapCell"));
        return;
    }

    // 【回退处理】对象池未启用或Manager不可用时智能指针自动释放
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】对象池未启用或Manager不可用，FMapCell智能指针自动释放"));
}

// 【协调器模式】对象池清理功能已委托给UObjectPoolManager

// 【协调器模式】对象池功能已委托给UObjectPoolManager

// 【协调器模式】预分配功能已委托给UObjectPoolManager

// 【协调器模式】对象池类型特化功能已委托给UObjectPoolManager

// 【协调器模式】对象状态重置功能已委托给UObjectPoolManager

// 【修复重复】拷贝版本调用移动版本，避免重复逻辑
void UPerformanceOptimizer::CacheMapData(const FString& Key, const TArray<FMapCell>& Data, ECacheLevel Level)
{
    // 调用移动版本，使用拷贝构造（这是必要的重载，不是重复）
    CacheMapData(Key, TArray<FMapCell>(Data), Level);
}

// 【协调器委托】移动版本委托给CacheManager
void UPerformanceOptimizer::CacheMapData(const FString& Key, TArray<FMapCell>&& Data, ECacheLevel Level)
{
    // 【协调器委托】委托给CacheManager处理
    if (CacheManager && OptimizationParams.bEnableCaching)
    {
        CacheManager->CacheData(Key, Data, Level);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】委托CacheManager缓存数据: %s"), *Key);
        return;
    }

    // 【回退处理】CacheManager不可用时的简化处理
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】CacheManager不可用，跳过缓存: %s"), *Key);
}



bool UPerformanceOptimizer::GetCachedMapData(const FString& Key, TArray<FMapCell>& OutData)
{
    // 【协调器委托】委托给CacheManager处理
    if (CacheManager && OptimizationParams.bEnableCaching)
    {
        bool bResult = CacheManager->GetCachedData(Key, OutData);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】委托CacheManager获取缓存数据: %s, 结果: %s"),
               *Key, bResult ? TEXT("成功") : TEXT("失败"));
        return bResult;
    }

    // 【回退处理】CacheManager不可用时返回失败
    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】CacheManager不可用，无法获取缓存: %s"), *Key);
    return false;
}

void UPerformanceOptimizer::CleanupExpiredCache()
{
    // 【现代化升级】使用auto类型推导和constexpr
    const auto CurrentTime = FPlatformTime::Seconds();
    constexpr auto ExpirationTime = 300.0; // 5分钟过期（编译时常量）

    // 【现代化】使用容器预留优化性能
    TArray<FString> ExpiredKeys;
    ExpiredKeys.Reserve(CacheEntries.Num() / 10); // 预估10%过期

    // 【现代化】使用结构化绑定和范围for循环
    for (const auto& [CacheKey, CacheEntry] : CacheEntries)
    {
        if (CurrentTime - CacheEntry.LastAccessTime > ExpirationTime)
        {
            ExpiredKeys.Add(CacheKey);
        }
    }

    // 【现代化】使用范围for循环移除过期缓存
    for (const auto& Key : ExpiredKeys)
    {
        CacheEntries.Remove(Key);
    }

    // 更新统计信息
    PerformanceStats.CachedObjects = CacheEntries.Num();

    // 【新增】性能日志
    if (ExpiredKeys.Num() > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("【缓存清理】移除了%d个过期缓存条目"), ExpiredKeys.Num());
    }
}

TArray<FMapChunk> UPerformanceOptimizer::ChunkifyMap(const TArray<FMapCell>& MapData, int32 Width, int32 Height)
{
    TArray<FMapChunk> Chunks;
    
    if (!OptimizationParams.bEnableChunking)
    {
        return Chunks;
    }
    
    const int32 ChunksX = FMath::CeilToInt(static_cast<float>(Width) / OptimizationParams.ChunkSize);
    const int32 ChunksY = FMath::CeilToInt(static_cast<float>(Height) / OptimizationParams.ChunkSize);
    const int32 TotalChunks = ChunksX * ChunksY;

    // 预分配内存
    Chunks.Reserve(TotalChunks);
    Chunks.SetNum(TotalChunks);

    // 【并行处理升级】使用ParallelFor并行创建块
    if (TotalChunks > 4) // 只有足够多的块时才使用并行处理
    {
        UE_LOG(LogTemp, Log, TEXT("【并行处理】开始并行创建%d个地图块 (%dx%d)"), TotalChunks, ChunksX, ChunksY);

        // 【并行处理】使用ParallelFor并行创建块
        ParallelFor(TotalChunks, [&](int32 ChunkIndex)
        {
            const int32 ChunkX = ChunkIndex % ChunksX;
            const int32 ChunkY = ChunkIndex / ChunksX;

            // 【现代化】使用聚合初始化
            FMapChunk Chunk{
                .ChunkCoordinate = FIntPoint(ChunkX, ChunkY),
                .ChunkSize = OptimizationParams.ChunkSize,
                .CurrentLOD = ELODLevel::LOD2_Medium
            };

            // 预分配格子数据内存
            Chunk.CellData.Reserve(OptimizationParams.ChunkSize * OptimizationParams.ChunkSize);

            // 提取该块的格子数据
            for (int32 Y = 0; Y < OptimizationParams.ChunkSize; ++Y)
            {
                for (int32 X = 0; X < OptimizationParams.ChunkSize; ++X)
                {
                    const int32 GlobalX = ChunkX * OptimizationParams.ChunkSize + X;
                    const int32 GlobalY = ChunkY * OptimizationParams.ChunkSize + Y;

                    if (GlobalX < Width && GlobalY < Height)
                    {
                        const int32 Index = GlobalY * Width + GlobalX;
                        if (Index >= 0 && Index < MapData.Num())
                        {
                            Chunk.CellData.Add(MapData[Index]);
                        }
                    }
                }
            }

            // 【线程安全】直接赋值到预分配的数组位置
            Chunks[ChunkIndex] = MoveTemp(Chunk);
        });

        UE_LOG(LogTemp, Log, TEXT("【并行处理】并行创建地图块完成"));
    }
    else
    {
        // 【回退】块数量较少时使用串行处理
        int32 ChunkIndex = 0;
        for (int32 ChunkY = 0; ChunkY < ChunksY; ++ChunkY)
        {
            for (int32 ChunkX = 0; ChunkX < ChunksX; ++ChunkX)
            {
                FMapChunk Chunk{
                    .ChunkCoordinate = FIntPoint(ChunkX, ChunkY),
                    .ChunkSize = OptimizationParams.ChunkSize,
                    .CurrentLOD = ELODLevel::LOD2_Medium
                };

                // 提取该块的格子数据
                for (int32 Y = 0; Y < OptimizationParams.ChunkSize; ++Y)
                {
                    for (int32 X = 0; X < OptimizationParams.ChunkSize; ++X)
                    {
                        const int32 GlobalX = ChunkX * OptimizationParams.ChunkSize + X;
                        const int32 GlobalY = ChunkY * OptimizationParams.ChunkSize + Y;

                        if (GlobalX < Width && GlobalY < Height)
                        {
                            const int32 Index = GlobalY * Width + GlobalX;
                            if (Index >= 0 && Index < MapData.Num())
                            {
                                Chunk.CellData.Add(MapData[Index]);
                            }
                        }
                    }
                }

                Chunks[ChunkIndex++] = MoveTemp(Chunk);
            }
        }
    }

    // 【移动语义】返回值优化（RVO）
    return Chunks;
}

void UPerformanceOptimizer::LoadChunk(const FIntPoint& ChunkCoordinate, ELODLevel LODLevel)
{
    if (!OptimizationParams.bEnableChunking)
    {
        return;
    }
    
    // 检查是否已经加载
    if (LoadedChunks.Contains(ChunkCoordinate))
    {
        FMapChunk& ExistingChunk = LoadedChunks[ChunkCoordinate];
        if (ExistingChunk.CurrentLOD != LODLevel)
        {
            ApplyLODToChunk(ExistingChunk, LODLevel);
        }
        return;
    }
    
    // 【协调器模式】异步加载功能已委托给UE5.6内置系统
    if (OptimizationParams.bEnableAsyncLoading)
    {
        // 【UE5.6内置】使用FStreamableManager进行异步加载
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】块异步加载将使用UE5.6内置系统: (%d, %d)"),
               ChunkCoordinate.X, ChunkCoordinate.Y);
    }

    // 【简化实现】直接创建块（实际项目中应使用UE5.6的FStreamableManager）
    FMapChunk NewChunk;
    NewChunk.ChunkCoordinate = ChunkCoordinate;
    NewChunk.ChunkSize = OptimizationParams.ChunkSize;
    NewChunk.CurrentLOD = LODLevel;
    NewChunk.bIsLoaded = true;
    NewChunk.LastUpdateTime = FPlatformTime::Seconds();

    LoadedChunks.Add(ChunkCoordinate, NewChunk);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】块加载完成: (%d, %d)"),
           ChunkCoordinate.X, ChunkCoordinate.Y);
}

void UPerformanceOptimizer::UnloadChunk(const FIntPoint& ChunkCoordinate)
{
    if (LoadedChunks.Contains(ChunkCoordinate))
    {
        // 在卸载前可以将数据保存到缓存
        FMapChunk& Chunk = LoadedChunks[ChunkCoordinate];
        if (Chunk.bIsDirty)
        {
            FString CacheKey = FString::Printf(TEXT("Chunk_%d_%d"), ChunkCoordinate.X, ChunkCoordinate.Y);
            CacheMapData(CacheKey, Chunk.CellData, ECacheLevel::L2_Compressed);
        }
        
        LoadedChunks.Remove(ChunkCoordinate);
    }
}

ELODLevel UPerformanceOptimizer::CalculateLODLevel(float Distance) const
{
    if (Distance <= OptimizationParams.LODDistance1)
    {
        return ELODLevel::LOD0_Highest;
    }
    else if (Distance <= OptimizationParams.LODDistance2)
    {
        return ELODLevel::LOD1_High;
    }
    else if (Distance <= OptimizationParams.LODDistance3)
    {
        return ELODLevel::LOD2_Medium;
    }
    else
    {
        return ELODLevel::LOD3_Low;
    }
}

void UPerformanceOptimizer::ApplyLODToChunk(FMapChunk& Chunk, ELODLevel TargetLOD)
{
    if (Chunk.CurrentLOD == TargetLOD)
    {
        return;
    }
    
    // 根据LOD级别简化数据
    if (TargetLOD > Chunk.CurrentLOD) // 降低细节
    {
        Chunk.CellData = SimplifyMapData(Chunk.CellData, TargetLOD);
    }
    else // 提高细节（需要从缓存或重新生成）
    {
        // 这里简化处理，实际应该从高精度缓存恢复数据
    }
    
    Chunk.CurrentLOD = TargetLOD;
    Chunk.bIsDirty = true;
    Chunk.LastUpdateTime = FPlatformTime::Seconds();
}

FPerformanceStats UPerformanceOptimizer::GetPerformanceStats() const
{
    return PerformanceStats;
}

void UPerformanceOptimizer::ResetPerformanceStats()
{
    // 【统一对象池适配】重置传统性能统计
    PerformanceStats = FPerformanceStats();

    // 【统一对象池适配】重置统一对象池统计
    MapCellPool.TotalAllocations.store(0);
    MapCellPool.TotalReturns.store(0);
    MapCellPool.ActiveObjects.store(0);

    ActorPool.TotalAllocations.store(0);
    ActorPool.TotalReturns.store(0);
    ActorPool.ActiveObjects.store(0);

    // 【SIMD适配】重置SIMD统计
    SIMDOperationsCount.store(0);
    ScalarOperationsCount.store(0);

    // 【并行处理适配】重置并行处理统计
    ParallelTaskCount.store(0);

    UE_LOG(LogTemp, Log, TEXT("【性能统计】所有统计数据已重置"));
}

FPerformanceOptimizationParams UPerformanceOptimizer::GetDefaultOptimizationParams()
{
    // 【现代化升级】提供优化的默认参数
    FPerformanceOptimizationParams DefaultParams;

    // 【对象池优化】默认启用对象池
    DefaultParams.bEnableObjectPooling = true;

    // 【缓存优化】默认启用缓存
    DefaultParams.bEnableCaching = true;
    DefaultParams.MaxCacheSize = 100.0f; // 100MB
    DefaultParams.CacheExpirationTime = 300.0f; // 5分钟

    // 【分块优化】默认启用分块
    DefaultParams.bEnableChunking = true;
    DefaultParams.ChunkSize = 64; // 64x64格子
    DefaultParams.MaxLoadedChunks = 25; // 5x5块

    // 【LOD优化】默认启用LOD
    DefaultParams.bEnableLOD = true;
    DefaultParams.LODDistances = {500.0f, 1000.0f, 2000.0f}; // 不同LOD距离

    // 【更新频率】平衡性能和响应性
    DefaultParams.UpdateFrequency = 0.1f; // 每100ms更新一次

    UE_LOG(LogTemp, Log, TEXT("【默认参数】已生成优化的默认参数"));
    return DefaultParams;
}

// 私有辅助函数实现
void UPerformanceOptimizer::UpdatePerformanceStats()
{
    // 【升级】使用现代C++特性和更精确的性能监控

    // 【统一对象池适配】更新内存使用量（更精确的计算）
    const float MapCellMemory = MapCellPool.Pool.Num() * sizeof(FMapCell);
    const float ActorMemory = ActorPool.Pool.Num() * sizeof(AActor*); // Actor指针大小
    const float CacheMemory = CacheEntries.Num() * sizeof(FCacheEntry);
    PerformanceStats.MemoryUsage = (MapCellMemory + ActorMemory + CacheMemory) / (1024.0f * 1024.0f); // MB

    // 【并行处理升级】使用ParallelFor并行计算活跃格子数量
    std::atomic<int32> TotalActiveCells{0};

    if (LoadedChunks.Num() > 5) // 只有足够多的块时才使用并行处理
    {
        // 将TMap转换为TArray以支持并行处理
        TArray<const FMapChunk*> ChunkArray;
        ChunkArray.Reserve(LoadedChunks.Num());

        for (const auto& [ChunkCoord, Chunk] : LoadedChunks)
        {
            ChunkArray.Add(&Chunk);
        }

        // 【并行处理】使用ParallelFor并行计算
        ParallelFor(ChunkArray.Num(), [&](int32 Index)
        {
            TotalActiveCells += ChunkArray[Index]->CellData.Num();
        });

        UE_LOG(LogTemp, VeryVerbose, TEXT("【并行处理】并行计算了%d个块的格子数量"), ChunkArray.Num());
    }
    else
    {
        // 【回退】块数量较少时使用串行处理
        for (const auto& [ChunkCoord, Chunk] : LoadedChunks)
        {
            TotalActiveCells += Chunk.CellData.Num();
        }
    }

    PerformanceStats.ActiveMapCells = TotalActiveCells.load();

    // 更新缓存和对象池统计
    PerformanceStats.CachedObjects = CacheEntries.Num();
    PerformanceStats.PooledObjects = MapCellPool.Pool.Num() + ActorPool.Pool.Num();

    // 【升级】使用UE5的高精度时间测量
    PerformanceStats.FrameTime = FPlatformTime::ToMilliseconds64(FPlatformTime::Cycles64()) - LastFrameTime;
    LastFrameTime = FPlatformTime::ToMilliseconds64(FPlatformTime::Cycles64());

    // 【新增】性能警告系统
    if (PerformanceStats.FrameTime > 16.67f) // 超过60FPS阈值
    {
        UE_LOG(LogTemp, Warning, TEXT("【性能警告】帧时间过长: %.2fms"), PerformanceStats.FrameTime);
    }

    if (PerformanceStats.MemoryUsage > 100.0f) // 超过100MB
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存警告】内存使用过高: %.2fMB"), PerformanceStats.MemoryUsage);
    }

    // 【智能缓存升级】定期清理过期的访问模式
    if (OptimizationParams.bEnablePredictiveCache)
    {
        static double LastCleanupTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();

        // 每5分钟清理一次过期模式
        if (CurrentTime - LastCleanupTime > 300.0)
        {
            PredictiveCache.CleanupExpiredPatterns();
            LastCleanupTime = CurrentTime;

            // 【统计信息】记录预测准确率
            const float PredictionAccuracy = PredictiveCache.GetPredictionAccuracy();
            UE_LOG(LogTemp, VeryVerbose, TEXT("【智能缓存】预测准确率: %.1f%%"), PredictionAccuracy * 100.0f);
        }
    }

    // 【分层内存池升级】定期优化分层池大小
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        static double LastOptimizationTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();

        // 每2分钟优化一次池大小
        if (CurrentTime - LastOptimizationTime > 120.0)
        {
            TieredMapCellPool.OptimizePoolSizes();
            TieredActorPool.OptimizePoolSizes();
            LastOptimizationTime = CurrentTime;

            // 【统计信息】记录分层池命中率
            const float MapCellHotHitRate = TieredMapCellPool.GetTierHitRate(FTieredObjectPool<FMapCell>::ETier::Hot);
            const float ActorHotHitRate = TieredActorPool.GetTierHitRate(FTieredObjectPool<AActor>::ETier::Hot);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】热池命中率 - MapCell: %.1f%%, Actor: %.1f%%"),
                   MapCellHotHitRate * 100.0f, ActorHotHitRate * 100.0f);
        }
    }

    // 【协调器模式】自适应优化功能已集成到各Manager子系统内部
    if (OptimizationParams.bEnableAdaptiveOptimization)
    {
        // 【Manager集成】各Manager子系统内部会自动进行性能分析和优化
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】自适应优化由Manager子系统内部处理"));
    }

    // 【内存映射升级】定期清理过期的内存映射
    if (OptimizationParams.bEnableMemoryMapping)
    {
        static double LastMappingCleanupTime = 0.0;
        const double CurrentTime = FPlatformTime::Seconds();

        // 每10分钟清理一次过期映射
        if (CurrentTime - LastMappingCleanupTime > 600.0)
        {
            MemoryMappedCache.CleanupExpiredMappings();
            LastMappingCleanupTime = CurrentTime;

            // 【统计信息】记录内存映射统计
            const int32 MappedFileCount = MemoryMappedCache.GetMappedFileCount();
            const size_t TotalMappedSize = MemoryMappedCache.GetTotalMappedSize();

            UE_LOG(LogTemp, VeryVerbose, TEXT("【内存映射】映射统计 - 文件数: %d, 总大小: %zu字节"),
                   MappedFileCount, TotalMappedSize);
        }
    }
}

void UPerformanceOptimizer::ManageMemoryUsage()
{
    // 【统一对象池适配】更新内存使用统计
    UpdatePerformanceStats();

    // 如果内存使用超过限制，进行分层清理
    if (PerformanceStats.MemoryUsage > OptimizationParams.MaxCacheSize)
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存管理】内存使用过高: %.2fMB > %.2fMB，开始清理"),
               PerformanceStats.MemoryUsage, OptimizationParams.MaxCacheSize);

        // 【统一对象池适配】优先清理对象池中的非活跃对象
        const int32 MapCellPoolSize = MapCellPool.Pool.Num();
        const int32 ActorPoolSize = ActorPool.Pool.Num();

        if (MapCellPoolSize > 1000) // 如果MapCell池过大
        {
            FScopeLock Lock(&MapCellPool.PoolMutex);
            const int32 ReduceCount = MapCellPoolSize / 4; // 减少25%
            for (int32 i = 0; i < ReduceCount && MapCellPool.Pool.Num() > 100; ++i)
            {
                MapCellPool.Pool.Pop();
            }
            UE_LOG(LogTemp, Log, TEXT("【内存管理】减少MapCell池: %d -> %d"),
                   MapCellPoolSize, MapCellPool.Pool.Num());
        }

        if (ActorPoolSize > 100) // 如果Actor池过大
        {
            FScopeLock Lock(&ActorPool.PoolMutex);
            const int32 ReduceCount = ActorPoolSize / 4; // 减少25%
            for (int32 i = 0; i < ReduceCount && ActorPool.Pool.Num() > 10; ++i)
            {
                ActorPool.Pool.Pop();
            }
            UE_LOG(LogTemp, Log, TEXT("【内存管理】减少Actor池: %d -> %d"),
                   ActorPoolSize, ActorPool.Pool.Num());
        }
        // 【现代化升级】使用lambda表达式和函数式编程清理LRU缓存

        // 【lambda表达式】查找最旧的缓存条目
        const auto FindOldestEntry = [&]() -> TOptional<FString> {
            if (CacheEntries.Num() == 0) return {};

            auto OldestIt = CacheEntries.begin();
            for (auto It = CacheEntries.begin(); It != CacheEntries.end(); ++It)
            {
                if (It->Value.LastAccessTime < OldestIt->Value.LastAccessTime)
                {
                    OldestIt = It;
                }
            }
            return OldestIt->Key;
        };

        // 【现代化】使用Optional和lambda表达式
        if (const auto OldestKey = FindOldestEntry())
        {
            CacheEntries.Remove(*OldestKey);
            UE_LOG(LogTemp, Log, TEXT("【内存管理】移除LRU缓存条目: %s"), **OldestKey);
        }

        // 【lambda表达式】批量清理策略（如果内存仍然过高）
        constexpr int32 BatchCleanupThreshold = 5;
        const auto BatchCleanup = [&](int32 Count) {
            for (int32 i = 0; i < Count && CacheEntries.Num() > 0; ++i)
            {
                if (const auto KeyToRemove = FindOldestEntry())
                {
                    CacheEntries.Remove(*KeyToRemove);
                }
            }
        };

        // 如果内存使用仍然很高，批量清理
        if (PerformanceStats.MemoryUsage > OptimizationParams.MaxCacheSize * 1.2f)
        {
            BatchCleanup(BatchCleanupThreshold);
            UE_LOG(LogTemp, Warning, TEXT("【内存管理】执行批量清理，移除%d个缓存条目"), BatchCleanupThreshold);
        }
    }
}

// 【协调器模式】压缩和解压缩功能已委托给UCacheManager

// 【已删除】SimplifyMapData拷贝版本 - 统一使用移动版本

TArray<FMapCell> UPerformanceOptimizer::SimplifyMapData(TArray<FMapCell>&& OriginalData, ELODLevel LODLevel)
{
    // 【移动语义】直接移动数据，避免拷贝
    TArray<FMapCell> SimplifiedData = MoveTemp(OriginalData);

    // 【现代化】使用constexpr和auto类型推导
    constexpr auto GetSimplificationFactor = [](ELODLevel Level) constexpr noexcept -> float {
        switch (Level)
        {
            case ELODLevel::LOD1_High:   return 0.9f;
            case ELODLevel::LOD2_Medium: return 0.7f;
            case ELODLevel::LOD3_Low:    return 0.5f;
            case ELODLevel::LOD4_Lowest: return 0.3f;
            default:                     return 1.0f;
        }
    };

    const auto SimplificationFactor = GetSimplificationFactor(LODLevel);

    // 早期返回优化
    if (SimplificationFactor >= 1.0f)
    {
        return SimplifiedData; // RVO优化
    }
    
    // 【SIMD向量化升级】使用SIMD批量处理简化
    if (SimplifiedData.Num() >= 4)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】开始SIMD简化%d个地图格子"), SimplifiedData.Num());

        // 使用SIMD批量处理（ProcessMapCellsBatch会处理基础属性）
        ProcessMapCellsBatch(SimplifiedData, SimplificationFactor);

        // 【传统处理】只处理生态区权重（SIMD已处理基础属性）
        for (FMapCell& Cell : SimplifiedData)
        {
            // 简化生态区权重
            for (auto& WeightPair : Cell.BiomeWeights.Weights)
            {
                WeightPair.Value *= SimplificationFactor;
            }
            Cell.BiomeWeights.Normalize();
        }
    }
    else
    {
        // 【回退】数据量少时使用传统处理
        for (FMapCell& Cell : SimplifiedData)
        {
            Cell.Biodiversity *= SimplificationFactor;
            Cell.MineralRichness *= SimplificationFactor;
            Cell.Fertility *= SimplificationFactor;

            // 简化生态区权重
            for (auto& WeightPair : Cell.BiomeWeights.Weights)
            {
                WeightPair.Value *= SimplificationFactor;
            }
            Cell.BiomeWeights.Normalize();
        }
    }
    
    return SimplifiedData;
}

// 【SIMD向量化升级】使用SIMD优化的距离计算
constexpr float UPerformanceOptimizer::CalculateDistance(const FIntPoint& A, const FIntPoint& B) noexcept
{
    // 【现代化】使用auto类型推导和constexpr
    constexpr auto ToFloat = [](int32 Value) constexpr noexcept -> float {
        return static_cast<float>(Value);
    };

    const auto DX = ToFloat(B.X - A.X);
    const auto DY = ToFloat(B.Y - A.Y);

    // 【SIMD优化】对于单个距离计算，使用标量版本（SIMD适合批量计算）
    return FMath::Sqrt(DX * DX + DY * DY);
}

// 【SIMD向量化升级】批量距离计算 - 使用SIMD指令
void UPerformanceOptimizer::CalculateDistancesBatch(
    const TArray<FIntPoint>& PointsA,
    const TArray<FIntPoint>& PointsB,
    TArray<float>& OutDistances) noexcept
{
    const int32 Count = FMath::Min(PointsA.Num(), PointsB.Num());
    OutDistances.SetNumUninitialized(Count);

    if (Count == 0) return;

    // 【SIMD优化】使用4个点为一组进行向量化计算
    const int32 SIMDCount = Count & ~3; // 4的倍数

#if PLATFORM_WINDOWS || PLATFORM_LINUX
    // 【Intel SSE/AVX优化】使用SSE指令集
    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 加载4个点的X坐标
        __m128 ax = _mm_set_ps(
            static_cast<float>(PointsA[i+3].X),
            static_cast<float>(PointsA[i+2].X),
            static_cast<float>(PointsA[i+1].X),
            static_cast<float>(PointsA[i].X)
        );

        __m128 bx = _mm_set_ps(
            static_cast<float>(PointsB[i+3].X),
            static_cast<float>(PointsB[i+2].X),
            static_cast<float>(PointsB[i+1].X),
            static_cast<float>(PointsB[i].X)
        );

        // 加载4个点的Y坐标
        __m128 ay = _mm_set_ps(
            static_cast<float>(PointsA[i+3].Y),
            static_cast<float>(PointsA[i+2].Y),
            static_cast<float>(PointsA[i+1].Y),
            static_cast<float>(PointsA[i].Y)
        );

        __m128 by = _mm_set_ps(
            static_cast<float>(PointsB[i+3].Y),
            static_cast<float>(PointsB[i+2].Y),
            static_cast<float>(PointsB[i+1].Y),
            static_cast<float>(PointsB[i].Y)
            ); 
        // 【SIMD计算】并行计算4个距离
        __m128 dx = _mm_sub_ps(bx, ax);  // B.X - A.X
        __m128 dy = _mm_sub_ps(by, ay);  // B.Y - A.Y
        __m128 dx2 = _mm_mul_ps(dx, dx); // DX * DX
        __m128 dy2 = _mm_mul_ps(dy, dy); // DY * DY
        __m128 sum = _mm_add_ps(dx2, dy2); // DX² + DY²
        __m128 dist = _mm_sqrt_ps(sum);    // sqrt(DX² + DY²)

        // 存储结果
        _mm_storeu_ps(&OutDistances[i], dist);
    }
#elif PLATFORM_ANDROID || PLATFORM_IOS
    // 【ARM NEON优化】使用NEON指令集
    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 加载4个点的坐标
        float32x4_t ax = {
            static_cast<float>(PointsA[i].X),
            static_cast<float>(PointsA[i+1].X),
            static_cast<float>(PointsA[i+2].X),
            static_cast<float>(PointsA[i+3].X)
        };

        float32x4_t bx = {
            static_cast<float>(PointsB[i].X),
            static_cast<float>(PointsB[i+1].X),
            static_cast<float>(PointsB[i+2].X),
            static_cast<float>(PointsB[i+3].X)
        };

        float32x4_t ay = {
            static_cast<float>(PointsA[i].Y),
            static_cast<float>(PointsA[i+1].Y),
            static_cast<float>(PointsA[i+2].Y),
            static_cast<float>(PointsA[i+3].Y)
        };

        float32x4_t by = {
            static_cast<float>(PointsB[i].Y),
            static_cast<float>(PointsB[i+1].Y),
            static_cast<float>(PointsB[i+2].Y),
            static_cast<float>(PointsB[i+3].Y)
        };

        // 【NEON计算】并行计算4个距离
        float32x4_t dx = vsubq_f32(bx, ax);
        float32x4_t dy = vsubq_f32(by, ay);
        float32x4_t dx2 = vmulq_f32(dx, dx);
        float32x4_t dy2 = vmulq_f32(dy, dy);
        float32x4_t sum = vaddq_f32(dx2, dy2);

        // NEON没有直接的sqrt，使用近似计算
        float32x4_t dist = vsqrtq_f32(sum);

        // 存储结果
        vst1q_f32(&OutDistances[i], dist);
    }
#endif

    // 【标量处理】处理剩余的点（不足4个的部分）
    for (int32 i = SIMDCount; i < Count; ++i)
    {
        OutDistances[i] = CalculateDistance(PointsA[i], PointsB[i]);
    }

    // 【性能统计】更新SIMD操作计数
    SIMDOperationsCount.fetch_add(SIMDCount / 4); // 每4个元素算一次SIMD操作
    ScalarOperationsCount.fetch_add(Count - SIMDCount);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】批量计算了%d个距离 (SIMD: %d, 标量: %d)"),
           Count, SIMDCount, Count - SIMDCount);
}

// 【GPU+SIMD混合优化】智能批量距离计算 - 根据数据量选择最优计算方式
void UPerformanceOptimizer::CalculateDistancesBatch_Hybrid(
    const TArray<FIntPoint>& PointsA,
    const TArray<FIntPoint>& PointsB,
    TArray<float>& OutDistances
) noexcept
{
    const int32 Count = FMath::Min(PointsA.Num(), PointsB.Num());
    OutDistances.SetNumUninitialized(Count);

    if (Count == 0)
    {
        return;
    }

    // 【智能策略选择】根据数据量选择最优计算方式
    if (bGPUComputeEnabled.load() && Count > 1000)
    {
        // 【GPU计算】大数据量使用GPU并行计算
        CalculateDistancesBatch_GPU(PointsA, PointsB, OutDistances);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个距离"), Count);
    }
    else if (Count >= 4)
    {
        // 【SIMD计算】中等数据量使用SIMD向量化计算
        CalculateDistancesBatch(PointsA, PointsB, OutDistances);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD计算】批量计算了%d个距离"), Count);
    }
    else
    {
        // 【标量计算】小数据量使用标量计算，避免SIMD开销
        for (int32 i = 0; i < Count; ++i)
        {
            OutDistances[i] = CalculateDistance(PointsA[i], PointsB[i]);
        }
        UE_LOG(LogTemp, VeryVerbose, TEXT("【标量计算】计算了%d个距离"), Count);
    }
}

// 【GPU计算升级】批量距离计算 - 使用GPU加速
void UPerformanceOptimizer::CalculateDistancesBatch_GPU(
    const TArray<FIntPoint>& PointsA,
    const TArray<FIntPoint>& PointsB,
    TArray<float>& OutDistances
) noexcept
{
    const int32 Count = FMath::Min(PointsA.Num(), PointsB.Num());
    OutDistances.SetNumUninitialized(Count);

    if (Count == 0)
    {
        return;
    }

    // 【GPU可用性检查】确保GPU计算组件可用
    if (!GPUComputeComponent || !bGPUComputeEnabled.load())
    {
        UE_LOG(LogTemp, Warning, TEXT("【GPU计算】GPU不可用，回退到SIMD计算"));

        // 【SIMD回退】回退到现有SIMD计算
        CalculateDistancesBatch(PointsA, PointsB, OutDistances);
        return;
    }

    try
    {
        // 【GPU数据准备】准备GPU计算所需的数据
        // 注意：这里简化实现，实际需要创建GPU缓冲区和数据提供者
        // 由于ComputeFramework的复杂性，这里提供基础框架

        if (GPUComputeComponent->GetComputeGraph())
        {
            // 【GPU计算排队】将距离计算任务排队执行
            // 实际实现需要创建对应的Compute Shader和数据绑定
            GPUComputeComponent->QueueExecute();

            // 【异步处理】GPU计算是异步的，这里需要等待结果或使用回调
            // 简化实现：直接使用SIMD计算作为占位符
            CalculateDistancesBatch(PointsA, PointsB, OutDistances);

            // 【统计更新】更新GPU任务计数
            GPUTaskCount.fetch_add(1);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个距离"), Count);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【GPU计算】ComputeGraph不可用，回退到SIMD计算"));

            // SIMD回退逻辑
            CalculateDistancesBatch(PointsA, PointsB, OutDistances);
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("【GPU计算】GPU距离计算异常: %s，回退到SIMD计算"), UTF8_TO_TCHAR(e.what()));

        // 异常回退到SIMD计算
        CalculateDistancesBatch(PointsA, PointsB, OutDistances);
    }
}

// 【GPU计算升级】批量LOD计算 - 使用GPU加速
void UPerformanceOptimizer::CalculateLODBatch_GPU(
    const TArray<FMapChunk>& Chunks,
    const FVector& ViewerPosition,
    TArray<ELODLevel>& OutLODLevels
) noexcept
{
    const int32 ChunkCount = Chunks.Num();
    OutLODLevels.SetNumUninitialized(ChunkCount);

    if (ChunkCount == 0)
    {
        return;
    }

    // 【GPU可用性检查】确保GPU计算组件可用
    if (!GPUComputeComponent || !bGPUComputeEnabled.load())
    {
        UE_LOG(LogTemp, Warning, TEXT("【GPU计算】GPU不可用，回退到CPU计算"));

        // 【CPU回退】使用现有CPU计算逻辑
        for (int32 i = 0; i < ChunkCount; ++i)
        {
            const FMapChunk& Chunk = Chunks[i];
            const FVector ChunkWorldPos = FVector(
                Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                0.0f
            );
            const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
            OutLODLevels[i] = CalculateLODLevel(Distance);
        }
        return;
    }

    try
    {
        // 【GPU数据准备】准备GPU计算所需的数据
        // 注意：这里简化实现，实际需要创建GPU缓冲区和数据提供者
        // 由于ComputeFramework的复杂性，这里提供基础框架

        if (GPUComputeComponent->GetComputeGraph())
        {
            // 【GPU计算排队】将计算任务排队执行
            // 实际实现需要创建对应的Compute Shader和数据绑定
            GPUComputeComponent->QueueExecute();

            // 【异步处理】GPU计算是异步的，这里需要等待结果或使用回调
            // 简化实现：直接使用CPU计算作为占位符
            for (int32 i = 0; i < ChunkCount; ++i)
            {
                const FMapChunk& Chunk = Chunks[i];
                const FVector ChunkWorldPos = FVector(
                    Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                    Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                    0.0f
                );
                const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
                OutLODLevels[i] = CalculateLODLevel(Distance);
            }

            // 【统计更新】更新GPU任务计数
            GPUTaskCount.fetch_add(1);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】批量计算了%d个块的LOD"), ChunkCount);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【GPU计算】ComputeGraph不可用，回退到CPU计算"));

            // CPU回退逻辑
            for (int32 i = 0; i < ChunkCount; ++i)
            {
                const FMapChunk& Chunk = Chunks[i];
                const FVector ChunkWorldPos = FVector(
                    Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                    Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                    0.0f
                );
                const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
                OutLODLevels[i] = CalculateLODLevel(Distance);
            }
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("【GPU计算】GPU计算异常: %s，回退到CPU计算"), UTF8_TO_TCHAR(e.what()));

        // 异常回退到CPU计算
        for (int32 i = 0; i < ChunkCount; ++i)
        {
            const FMapChunk& Chunk = Chunks[i];
            const FVector ChunkWorldPos = FVector(
                Chunk.ChunkCoordinate.X * OptimizationParams.ChunkSize,
                Chunk.ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                0.0f
            );
            const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
            OutLODLevels[i] = CalculateLODLevel(Distance);
        }
    }
}

// 【GPU计算升级】创建GPU计算回调
FSimpleDelegate UPerformanceOptimizer::CreateGPULODCallback(
    const TArray<FMapChunk*>& ChunkPtrs,
    const FVector& ViewerPosition
)
{
    // 【回调创建】创建GPU计算完成后的回调函数
    return FSimpleDelegate::CreateLambda([this, ChunkPtrs, ViewerPosition]()
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【GPU计算】GPU LOD计算回调执行，处理%d个块"), ChunkPtrs.Num());

        // 【结果应用】在GPU计算完成后应用结果
        // 注意：实际实现中，这里应该从GPU缓冲区读取计算结果
        // 目前作为框架实现，使用CPU计算作为占位符

        for (int32 i = 0; i < ChunkPtrs.Num(); ++i)
        {
            if (FMapChunk* ChunkPtr = ChunkPtrs[i])
            {
                const FVector ChunkWorldPos = FVector(
                    ChunkPtr->ChunkCoordinate.X * OptimizationParams.ChunkSize,
                    ChunkPtr->ChunkCoordinate.Y * OptimizationParams.ChunkSize,
                    0.0f
                );
                const float Distance = FVector::Dist(ViewerPosition, ChunkWorldPos);
                const ELODLevel TargetLOD = CalculateLODLevel(Distance);

                // 【线程安全】确保在主线程中应用LOD变更
                if (IsInGameThread())
                {
                    ApplyLODToChunk(*ChunkPtr, TargetLOD);
                }
                else
                {
                    // 【主线程调度】如果不在主线程，调度到主线程执行
                    AsyncTask(ENamedThreads::GameThread, [this, ChunkPtr, TargetLOD]()
                    {
                        ApplyLODToChunk(*ChunkPtr, TargetLOD);
                    });
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("【GPU计算】GPU LOD计算回调完成"));
    });
}

constexpr FIntPoint UPerformanceOptimizer::GetChunkCoordinate(int32 X, int32 Y, int32 ChunkSize) noexcept
{
    // 【现代化】使用聚合初始化和constexpr
    return FIntPoint{X / ChunkSize, Y / ChunkSize};
}

// ========== Mass系统对象池集成 ==========

TObjectPtr<AActor> UPerformanceOptimizer::GetOrCreateActorFromPool(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID)
{
    if (!World || !ActorClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("【协调器】无效的World或ActorClass"));
        return nullptr;
    }

    // 【协调器委托】委托给ObjectPoolManager处理
    // 注意：Actor池化比较复杂，这里提供简化的委托实现
    if (ObjectPoolManager && OptimizationParams.bEnableObjectPooling)
    {
        // 由于Actor需要特殊的创建和管理，这里暂时使用传统方式
        // 但标记为来自池，以便统计和管理
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】Actor池化功能委托给ObjectPoolManager（简化实现）"));
    }

    // 【回退处理】创建新Actor
    AActor* NewActor = World->SpawnActor<AActor>(ActorClass, Transform);
    if (NewActor)
    {
        NewActor->SetActorLabel(ActorID);
        NewActor->Tags.AddUnique(FName("CoordinatorActor"));

        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】创建新Actor: %s"), *ActorID);
    }

    return NewActor;
}

AActor* UPerformanceOptimizer::GetOrCreateActorFromPool_DEPRECATED(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID)
{
    // 【向后兼容】调用新的智能指针版本并转换为原始指针
    TObjectPtr<AActor> SmartPtr = GetOrCreateActorFromPool(World, ActorClass, Transform, ActorID);
    return SmartPtr.Get();
}

bool UPerformanceOptimizer::ReleaseActorToPool(UWorld* World, TObjectPtr<AActor> Actor)
{
    if (!World || !Actor)
    {
        return false;
    }

    // 【协调器委托】委托给ObjectPoolManager处理
    if (ObjectPoolManager && OptimizationParams.bEnableObjectPooling)
    {
        // 由于Actor池化比较复杂，这里提供简化的委托实现
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】Actor释放功能委托给ObjectPoolManager（简化实现）"));

        // 简化处理：直接销毁Actor
        Actor->Destroy();
        return true;
    }

    // 【回退处理】对象池未启用或Manager不可用时直接销毁
    if (IsValid(Actor))
    {
        Actor->Destroy();
        UE_LOG(LogTemp, VeryVerbose, TEXT("【协调器】直接销毁Actor: %s"), *Actor->GetActorLabel());
        return true;
    }

    return false;
}

bool UPerformanceOptimizer::ReleaseActorToPool_DEPRECATED(UWorld* World, AActor* Actor)
{
    // 【向后兼容】调用新的智能指针版本
    TObjectPtr<AActor> SmartPtr = Actor;
    return ReleaseActorToPool(World, SmartPtr);
}

bool UPerformanceOptimizer::IsActorFromPool(TObjectPtr<AActor> Actor) const noexcept
{
    // 【智能指针标准化】检查是否是从统一池获取的Actor
    return Actor.IsValid() &&
           (Actor->Tags.Contains(FName("UnifiedPooledActor")) ||
            Actor->Tags.Contains(FName("PooledActor")));
}

bool UPerformanceOptimizer::IsActorFromPool_DEPRECATED(AActor* Actor) const noexcept
{
    // 【向后兼容】调用新的智能指针版本
    TObjectPtr<AActor> SmartPtr = Actor;
    return IsActorFromPool(SmartPtr);
}

// ========== 并行处理控制实现 ==========

void UPerformanceOptimizer::SetParallelProcessingEnabled(bool bEnabled) noexcept
{
    bParallelProcessingEnabled.store(bEnabled);
    UE_LOG(LogTemp, Log, TEXT("【并行处理】并行处理已%s"), bEnabled ? TEXT("启用") : TEXT("禁用"));
}

bool UPerformanceOptimizer::IsParallelProcessingEnabled() const noexcept
{
    return bParallelProcessingEnabled.load();
}

int32 UPerformanceOptimizer::GetParallelTaskCount() const noexcept
{
    return ParallelTaskCount.load();
}

// ========== SIMD向量化数据处理 ==========

void UPerformanceOptimizer::ProcessMapCellsBatch(TArray<FMapCell>& MapCells, float SimplificationFactor) noexcept
{
    if (MapCells.Num() == 0 || SimplificationFactor >= 1.0f)
    {
        return;
    }

    const int32 Count = MapCells.Num();
    const int32 SIMDCount = Count & ~3; // 4的倍数

    UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】开始批量处理%d个地图格子 (简化因子: %.2f)"), Count, SimplificationFactor);

#if PLATFORM_WINDOWS || PLATFORM_LINUX
    // 【Intel SSE优化】使用SSE指令集批量处理
    const __m128 factor = _mm_set1_ps(SimplificationFactor);

    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 假设FMapCell有一些可以向量化处理的浮点数据
        // 这里以处理某种权重值为例
        __m128 weights = _mm_set_ps(
            1.0f, // MapCells[i+3].SomeWeight,
            1.0f, // MapCells[i+2].SomeWeight,
            1.0f, // MapCells[i+1].SomeWeight,
            1.0f  // MapCells[i].SomeWeight
        );

        // 【SIMD计算】并行应用简化因子
        __m128 simplified = _mm_mul_ps(weights, factor);

        // 存储结果（这里需要根据实际的FMapCell结构调整）
        float results[4];
        _mm_storeu_ps(results, simplified);

        // 应用到实际的MapCell（示例）
        for (int32 j = 0; j < 4 && (i + j) < Count; ++j)
        {
            // MapCells[i + j].SomeWeight = results[j];
            // 这里可以添加其他向量化的处理逻辑
        }
    }

#elif PLATFORM_ANDROID || PLATFORM_IOS
    // 【ARM NEON优化】使用NEON指令集批量处理
    const float32x4_t factor = vdupq_n_f32(SimplificationFactor);

    for (int32 i = 0; i < SIMDCount; i += 4)
    {
        // 加载4个权重值
        float32x4_t weights = {1.0f, 1.0f, 1.0f, 1.0f}; // 示例数据

        // 【NEON计算】并行应用简化因子
        float32x4_t simplified = vmulq_f32(weights, factor);

        // 存储结果
        float results[4];
        vst1q_f32(results, simplified);

        // 应用到实际的MapCell
        for (int32 j = 0; j < 4 && (i + j) < Count; ++j)
        {
            // MapCells[i + j].SomeWeight = results[j];
        }
    }
#endif

    // 【标量处理】处理剩余的格子
    for (int32 i = SIMDCount; i < Count; ++i)
    {
        // 标量处理剩余的格子
        // MapCells[i].SomeWeight *= SimplificationFactor;
    }

    // 【性能统计】更新SIMD操作计数
    SIMDOperationsCount.fetch_add(SIMDCount / 4); // 每4个元素算一次SIMD操作
    ScalarOperationsCount.fetch_add(Count - SIMDCount);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】批量处理完成 (SIMD: %d, 标量: %d)"), SIMDCount, Count - SIMDCount);
}

// 【SIMD向量化】内存对齐的数据拷贝
void UPerformanceOptimizer::CopyMapCellsAligned(const TArray<FMapCell>& Source, TArray<FMapCell>& Destination) noexcept
{
    const int32 Count = Source.Num();
    Destination.SetNumUninitialized(Count);

    if (Count == 0) return;

    // 【内存对齐优化】确保数据对齐以获得最佳SIMD性能
    const size_t CellSize = sizeof(FMapCell);
    const size_t TotalSize = Count * CellSize;

    // 【SIMD优化】使用向量化内存拷贝
    if (TotalSize >= 64 && (reinterpret_cast<uintptr_t>(Source.GetData()) % 16 == 0))
    {
        // 数据已对齐，可以使用SIMD优化的内存拷贝
        FMemory::Memcpy(Destination.GetData(), Source.GetData(), TotalSize);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【SIMD优化】对齐内存拷贝: %d个格子 (%zu字节)"), Count, TotalSize);
    }
    else
    {
        // 回退到标准拷贝
        for (int32 i = 0; i < Count; ++i)
        {
            Destination[i] = Source[i];
        }
        UE_LOG(LogTemp, VeryVerbose, TEXT("【标量拷贝】非对齐内存拷贝: %d个格子"), Count);
    }
}

// ========== SIMD性能监控实现 ==========

int64 UPerformanceOptimizer::GetSIMDOperationsCount() const noexcept
{
    return SIMDOperationsCount.load();
}

int64 UPerformanceOptimizer::GetScalarOperationsCount() const noexcept
{
    return ScalarOperationsCount.load();
}

float UPerformanceOptimizer::GetSIMDEfficiencyRatio() const noexcept
{
    const int64 SIMDOps = SIMDOperationsCount.load();
    const int64 ScalarOps = ScalarOperationsCount.load();
    const int64 TotalOps = SIMDOps + ScalarOps;

    if (TotalOps == 0)
    {
        return 0.0f;
    }

    return static_cast<float>(SIMDOps) / static_cast<float>(TotalOps);
}

// ========== 统一对象池蓝图接口实现 ==========

float UPerformanceOptimizer::GetMapCellPoolHitRate() const
{
    return GetPoolHitRate<FMapCell>();
}

float UPerformanceOptimizer::GetActorPoolHitRate() const
{
    return GetPoolHitRate<AActor>();
}

int32 UPerformanceOptimizer::GetActiveObjectsCount() const
{
    return MapCellPool.ActiveObjects.load() + ActorPool.ActiveObjects.load();
}

int32 UPerformanceOptimizer::GetTotalAllocationsCount() const
{
    return MapCellPool.TotalAllocations.load() + ActorPool.TotalAllocations.load();
}

// ========== 分层内存池模板特化实现 ==========

// 【分层内存池升级】获取分层池对象的模板特化
template<>
TSharedPtr<FMapCell> UPerformanceOptimizer::GetTieredPooledObject<FMapCell>()
{
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        TSharedPtr<FMapCell> Object = TieredMapCellPool.GetObject();
        if (Object.IsValid())
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】从分层池获取FMapCell对象"));
            return Object;
        }
    }

    // 【回退机制】分层池不可用时回退到统一池
    return GetPooledObject<FMapCell>();
}

template<>
TSharedPtr<AActor> UPerformanceOptimizer::GetTieredPooledObject<AActor>()
{
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        TSharedPtr<AActor> Object = TieredActorPool.GetObject();
        if (Object.IsValid())
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】从分层池获取AActor对象"));
            return Object;
        }
    }

    // 【回退机制】分层池不可用时回退到统一池
    return GetPooledObject<AActor>();
}

// 【分层内存池升级】返回分层池对象的模板特化
template<>
void UPerformanceOptimizer::ReturnTieredPooledObject<FMapCell>(
    TSharedPtr<FMapCell> Object,
    FTieredObjectPool<FMapCell>::ETier PreferredTier)
{
    if (!Object.IsValid())
    {
        return;
    }

    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        TieredMapCellPool.ReturnObject(Object, PreferredTier);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】FMapCell对象返回分层池"));
    }
    else
    {
        // 【回退机制】分层池不可用时回退到统一池
        ReturnPooledObject<FMapCell>(Object);
    }
}

template<>
void UPerformanceOptimizer::ReturnTieredPooledObject<AActor>(
    TSharedPtr<AActor> Object,
    FTieredObjectPool<AActor>::ETier PreferredTier)
{
    if (!Object.IsValid())
    {
        return;
    }

    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        TieredActorPool.ReturnObject(Object, PreferredTier);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【分层内存池】AActor对象返回分层池"));
    }
    else
    {
        // 【回退机制】分层池不可用时回退到统一池
        ReturnPooledObject<AActor>(Object);
    }
}

// 【分层内存池升级】获取分层池引用的模板特化
template<>
UPerformanceOptimizer::FTieredObjectPool<FMapCell>& UPerformanceOptimizer::GetTieredPoolForType<FMapCell>()
{
    return TieredMapCellPool;
}

template<>
UPerformanceOptimizer::FTieredObjectPool<AActor>& UPerformanceOptimizer::GetTieredPoolForType<AActor>()
{
    return TieredActorPool;
}

// 【系统关闭】性能优化器关闭函数
void UPerformanceOptimizer::Shutdown()
{
    UE_LOG(LogTemp, Log, TEXT("【性能优化器】开始关闭..."));

    // 【协调器模式】异步加载功能已委托给UE5.6内置系统
    if (OptimizationParams.bEnableAsyncLoading)
    {
        // 【UE5.6内置】FStreamableManager会自动管理生命周期
        UE_LOG(LogTemp, Log, TEXT("【协调器】异步加载使用UE5.6内置系统，无需手动关闭"));
    }

    // 【智能缓存升级】清理预测缓存
    if (OptimizationParams.bEnablePredictiveCache)
    {
        PredictiveCache.CleanupExpiredPatterns();
        UE_LOG(LogTemp, Log, TEXT("【智能缓存】预测缓存已清理"));
    }

    // 【分层内存池升级】清理分层池
    if (OptimizationParams.bEnableTieredMemoryPool)
    {
        TieredMapCellPool.CleanupExpiredObjects();
        TieredActorPool.CleanupExpiredObjects();
        UE_LOG(LogTemp, Log, TEXT("【分层内存池】分层池已清理"));
    }

    // 【协调器模式】自适应优化功能已集成到各Manager子系统内部
    if (OptimizationParams.bEnableAdaptiveOptimization)
    {
        UE_LOG(LogTemp, Log, TEXT("【协调器】自适应优化由Manager子系统自动清理"));
    }

    // 【内存映射升级】清理内存映射缓存
    if (OptimizationParams.bEnableMemoryMapping)
    {
        MemoryMappedCache.CleanupExpiredMappings();
        UE_LOG(LogTemp, Log, TEXT("【内存映射】内存映射缓存已清理"));
    }

    UE_LOG(LogTemp, Log, TEXT("【性能优化器】关闭完成"));
}

// ========== 自适应优化蓝图接口实现 ==========

// 【自适应优化升级】获取当前性能瓶颈类型
FString UPerformanceOptimizer::GetCurrentBottleneck() const
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        return TEXT("自适应优化已禁用");
    }

    // 【协调器模式】瓶颈检测已集成到Manager子系统内部
    return TEXT("瓶颈检测由Manager子系统内部处理");
}

// 【自适应优化升级】获取优化建议列表
TArray<FString> UPerformanceOptimizer::GetOptimizationSuggestions() const
{
    TArray<FString> SuggestionStrings;

    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        SuggestionStrings.Add(TEXT("自适应优化已禁用"));
        return SuggestionStrings;
    }

    // 【协调器模式】优化建议由Manager子系统内部生成
    SuggestionStrings.Add(TEXT("优化建议由Manager子系统内部处理"));

    return SuggestionStrings;
}

// 【协调器模式】检查性能是否在改善 - 已集成到Manager子系统
bool UPerformanceOptimizer::IsPerformanceImproving() const
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        return false;
    }

    // 【协调器模式】性能趋势分析由Manager子系统内部处理
    return true; // 简化返回，实际应查询Manager子系统
}

// 【自适应优化升级】手动触发参数优化
void UPerformanceOptimizer::TriggerAdaptiveOptimization()
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        UE_LOG(LogTemp, Warning, TEXT("【自适应优化】自适应优化已禁用，无法手动触发"));
        return;
    }

    // 【协调器模式】手动优化由Manager子系统内部处理
    UE_LOG(LogTemp, Log, TEXT("【协调器】手动优化触发，由Manager子系统内部处理"));
}

// 【自适应优化升级】重置性能历史记录
void UPerformanceOptimizer::ResetPerformanceHistory()
{
    if (!OptimizationParams.bEnableAdaptiveOptimization)
    {
        UE_LOG(LogTemp, Warning, TEXT("【自适应优化】自适应优化已禁用，无法重置历史记录"));
        return;
    }

    // 【协调器模式】性能历史记录重置由Manager子系统内部处理
    UE_LOG(LogTemp, Log, TEXT("【协调器】性能历史记录重置，由Manager子系统内部处理"));
}