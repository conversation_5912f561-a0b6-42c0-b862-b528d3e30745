// 【异步流式加载升级】异步块加载器系统实现
// 基于UE5的AsyncTask框架实现异步分块加载系统

#include "LevelGen/PerformanceOptimizer.h"
#include "Engine/Engine.h"
#include "Async/Async.h"
#include "HAL/PlatformTime.h"

// 【异步加载】异步加载地图块
TFuture<FMapChunk> FAsyncChunkLoader::LoadChunkAsync(const FIntPoint& ChunkCoord, ELoadPriority Priority)
{
    // 【请求创建】创建加载请求
    FChunkLoadRequest Request;
    Request.ChunkCoordinate = ChunkCoord;
    Request.Priority = Priority;
    Request.TargetLOD = ELODLevel::Medium;  // 默认中等LOD
    Request.RequestTime = FPlatformTime::Seconds();

    // 【队列管理】将请求添加到对应优先级队列
    {
        FScopeLock Lock(&LoadQueueMutex);
        
        // 确保优先级队列已初始化
        if (PriorityQueues.Num() != 5)
        {
            PriorityQueues.SetNum(5);
        }
        
        const int32 PriorityIndex = static_cast<int32>(Priority);
        PriorityQueues[PriorityIndex].Enqueue(Request);
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】块加载请求已排队: (%d, %d) 优先级: %d"), 
               ChunkCoord.X, ChunkCoord.Y, PriorityIndex);
    }

    // 【异步任务创建】创建异步加载任务
    TPromise<FMapChunk> Promise;
    TFuture<FMapChunk> Future = Promise.GetFuture();

    // 【后台执行】在线程池中执行加载任务
    Async(EAsyncExecution::ThreadPool, [this, ChunkCoord, Promise = MoveTemp(Promise)]() mutable
    {
        try
        {
            ActiveLoadTasks.fetch_add(1);
            
            // 【实际加载】执行块数据加载
            FMapChunk LoadedChunk;
            LoadedChunk.ChunkCoordinate = ChunkCoord;
            LoadedChunk.CurrentLOD = ELODLevel::Medium;
            
            // 【模拟加载】这里应该是实际的数据加载逻辑
            // 为了演示，我们创建一个基本的块数据
            LoadedChunk.MapCells.SetNum(64 * 64);  // 假设64x64的块大小
            
            for (int32 i = 0; i < LoadedChunk.MapCells.Num(); ++i)
            {
                FMapCell& Cell = LoadedChunk.MapCells[i];
                Cell.X = ChunkCoord.X * 64 + (i % 64);
                Cell.Y = ChunkCoord.Y * 64 + (i / 64);
                Cell.Height = 0.0f;
                Cell.BiomeWeights.SetNum(4);
                Cell.BiomeWeights[0] = 1.0f;  // 默认生物群系权重
            }
            
            // 【加载延迟模拟】模拟实际加载时间
            FPlatformProcess::Sleep(0.01f);  // 10ms加载时间
            
            // 【成功完成】设置Promise结果
            Promise.SetValue(LoadedChunk);
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】块加载完成: (%d, %d)"), 
                   ChunkCoord.X, ChunkCoord.Y);
        }
        catch (const std::exception& e)
        {
            UE_LOG(LogTemp, Error, TEXT("【异步加载】块加载异常: (%d, %d) - %s"), 
                   ChunkCoord.X, ChunkCoord.Y, UTF8_TO_TCHAR(e.what()));
            
            // 【异常处理】创建空块作为错误恢复
            FMapChunk EmptyChunk;
            EmptyChunk.ChunkCoordinate = ChunkCoord;
            EmptyChunk.CurrentLOD = ELODLevel::Culled;
            Promise.SetValue(EmptyChunk);
        }
        
        ActiveLoadTasks.fetch_sub(1);
    });

    return Future;
}

// 【流式预加载】流式预加载指定位置周围的块
void FAsyncChunkLoader::StreamLoadAroundPosition(const FVector& Position, float Radius)
{
    // 【中心块计算】计算位置对应的块坐标
    const int32 ChunkSize = 64;  // 假设块大小为64单位
    const FIntPoint CenterChunk = FIntPoint(
        static_cast<int32>(Position.X) / ChunkSize,
        static_cast<int32>(Position.Y) / ChunkSize
    );
    
    const int32 RadiusInChunks = FMath::CeilToInt(Radius / ChunkSize);
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】开始流式预加载 - 中心: (%d, %d), 半径: %d块"), 
           CenterChunk.X, CenterChunk.Y, RadiusInChunks);
    
    // 【智能预加载】根据距离设置不同的加载优先级
    for (int32 Y = -RadiusInChunks; Y <= RadiusInChunks; ++Y)
    {
        for (int32 X = -RadiusInChunks; X <= RadiusInChunks; ++X)
        {
            const FIntPoint ChunkCoord = CenterChunk + FIntPoint(X, Y);
            const float Distance = FMath::Sqrt(static_cast<float>(X * X + Y * Y));
            
            // 【优先级分配】根据距离分配加载优先级
            ELoadPriority Priority;
            if (Distance <= 1.0f)
            {
                Priority = ELoadPriority::Critical;    // 玩家当前位置
            }
            else if (Distance <= 2.0f)
            {
                Priority = ELoadPriority::High;        // 玩家附近
            }
            else if (Distance <= 4.0f)
            {
                Priority = ELoadPriority::Normal;      // 预加载区域
            }
            else if (Distance <= 6.0f)
            {
                Priority = ELoadPriority::Low;         // 远距离预测
            }
            else
            {
                Priority = ELoadPriority::Background;  // 后台预加载
            }
            
            // 【异步加载】启动异步加载任务
            LoadChunkAsync(ChunkCoord, Priority);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("【异步加载】流式预加载已启动 - 总块数: %d"), 
           (2 * RadiusInChunks + 1) * (2 * RadiusInChunks + 1));
}

// 【优先级管理】设置块的加载优先级
void FAsyncChunkLoader::SetChunkPriority(const FIntPoint& ChunkCoord, ELoadPriority Priority)
{
    FScopeLock Lock(&LoadQueueMutex);
    
    // 【队列搜索】在所有优先级队列中查找指定块
    for (int32 QueueIndex = 0; QueueIndex < PriorityQueues.Num(); ++QueueIndex)
    {
        TQueue<FChunkLoadRequest>& Queue = PriorityQueues[QueueIndex];
        
        // 注意：TQueue不支持直接遍历，这里需要重新实现
        // 简化实现：记录优先级变更请求
        UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】块优先级变更请求: (%d, %d) -> %d"), 
               ChunkCoord.X, ChunkCoord.Y, static_cast<int32>(Priority));
    }
}

// 【队列处理】处理加载队列，执行优先级最高的加载任务
void FAsyncChunkLoader::ProcessLoadQueue()
{
    FScopeLock Lock(&LoadQueueMutex);
    
    // 【并发控制】检查当前活跃任务数量
    const int32 CurrentActiveTasks = ActiveLoadTasks.load();
    if (CurrentActiveTasks >= MAX_CONCURRENT_LOADS)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】达到最大并发加载数限制: %d"), CurrentActiveTasks);
        return;
    }
    
    // 【优先级处理】按优先级顺序处理队列
    for (int32 PriorityIndex = 0; PriorityIndex < PriorityQueues.Num(); ++PriorityIndex)
    {
        TQueue<FChunkLoadRequest>& Queue = PriorityQueues[PriorityIndex];
        
        FChunkLoadRequest Request;
        if (Queue.Dequeue(Request))
        {
            // 【超时检查】检查请求是否超时
            const double CurrentTime = FPlatformTime::Seconds();
            const double RequestAge = CurrentTime - Request.RequestTime;
            
            if (RequestAge > LOAD_TIMEOUT)
            {
                UE_LOG(LogTemp, Warning, TEXT("【异步加载】请求超时，跳过: (%d, %d) 年龄: %.2fs"), 
                       Request.ChunkCoordinate.X, Request.ChunkCoordinate.Y, RequestAge);
                continue;
            }
            
            // 【任务执行】执行加载任务
            LoadChunkAsync(Request.ChunkCoordinate, Request.Priority);
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】处理队列请求: (%d, %d) 优先级: %d"), 
                   Request.ChunkCoordinate.X, Request.ChunkCoordinate.Y, PriorityIndex);
            
            break;  // 每次只处理一个请求，避免阻塞
        }
    }
}

// 【任务取消】取消指定块的加载请求
void FAsyncChunkLoader::CancelLoadRequest(const FIntPoint& ChunkCoord)
{
    FScopeLock Lock(&LoadQueueMutex);
    
    // 【队列清理】从所有优先级队列中移除指定块的请求
    // 注意：TQueue不支持随机访问，这里记录取消请求
    UE_LOG(LogTemp, VeryVerbose, TEXT("【异步加载】取消加载请求: (%d, %d)"), 
           ChunkCoord.X, ChunkCoord.Y);
}

// 【超时清理】清理超时的加载任务
void FAsyncChunkLoader::CleanupTimeoutTasks()
{
    FScopeLock Lock(&LoadQueueMutex);
    
    const double CurrentTime = FPlatformTime::Seconds();
    int32 CleanedTasks = 0;
    
    // 【队列清理】清理所有队列中的超时请求
    for (TQueue<FChunkLoadRequest>& Queue : PriorityQueues)
    {
        // 简化实现：记录清理操作
        // 实际实现需要重建队列以移除超时请求
    }
    
    if (CleanedTasks > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("【异步加载】清理了 %d 个超时任务"), CleanedTasks);
    }
}

// 【初始化】初始化异步加载器
void FAsyncChunkLoader::Initialize()
{
    FScopeLock Lock(&LoadQueueMutex);
    
    // 【队列初始化】初始化5个优先级队列
    PriorityQueues.SetNum(5);
    LoadingTasks.Empty();
    
    // 【统计重置】重置统计信息
    ActiveLoadTasks.store(0);
    
    UE_LOG(LogTemp, Log, TEXT("【异步加载】异步块加载器初始化完成"));
}

// 【关闭】关闭异步加载器，等待所有任务完成
void FAsyncChunkLoader::Shutdown()
{
    UE_LOG(LogTemp, Log, TEXT("【异步加载】开始关闭异步加载器..."));
    
    // 【等待完成】等待所有活跃任务完成
    const double StartTime = FPlatformTime::Seconds();
    const double MaxWaitTime = 10.0;  // 最多等待10秒
    
    while (ActiveLoadTasks.load() > 0)
    {
        const double CurrentTime = FPlatformTime::Seconds();
        if (CurrentTime - StartTime > MaxWaitTime)
        {
            UE_LOG(LogTemp, Warning, TEXT("【异步加载】关闭超时，强制结束 (剩余任务: %d)"), 
                   ActiveLoadTasks.load());
            break;
        }
        
        FPlatformProcess::Sleep(0.1f);  // 等待100ms
    }
    
    // 【清理资源】清理所有队列和任务
    {
        FScopeLock Lock(&LoadQueueMutex);
        PriorityQueues.Empty();
        LoadingTasks.Empty();
    }
    
    UE_LOG(LogTemp, Log, TEXT("【异步加载】异步块加载器已关闭"));
}
