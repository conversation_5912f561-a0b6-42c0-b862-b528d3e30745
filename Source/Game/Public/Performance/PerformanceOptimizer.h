#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

// 【并行处理升级】并行计算支持
#include "HAL/ThreadSafeBool.h"
#include "LevelGen/MapUtil.h"
#include <atomic>

// 【GPU计算升级】GPU加速计算支持
#include "ComputeFramework/ComputeGraphComponent.h"

// 【内存池高级优化】分层内存池支持
#include "HAL/CriticalSection.h"
#include "Containers/LockFreeList.h"
#include <memory_resource>

// 【UE5.6优化】添加必要的头文件包含
#include "Engine/StreamableManager.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetRegistry/IAssetRegistry.h"
#include "HAL/PlatformMemory.h"

// 【协调器模式】前向声明Manager子系统（避免循环依赖）
class UCacheManager;
class UObjectPoolManager;
class UGPUComputeManager;

// 【协调器模式】前向声明需要的结构体
struct FCacheEntry;
enum class ECacheLevel : uint8;

// 【压缩系统】包含CacheManager以使用统一的压缩枚举定义
#include "Performance/CacheManager.h"

// ========== 【压缩系统】压缩相关定义 ==========
// 注意：ECompressionAlgorithm枚举已在CacheManager.h中统一定义

/** 【压缩结果】压缩操作结果结构体 */
struct FCompressionResult
{
    /** 压缩是否成功 */
    bool bSuccess = false;

    /** 压缩后的数据 */
    TArray<uint8> CompressedData;

    /** 使用的压缩算法 */
    ECompressionAlgorithm Algorithm = ECompressionAlgorithm::None;

    /** 压缩比率 (0.0-1.0) */
    float CompressionRatio = 1.0f;

    /** 压缩耗时(毫秒) */
    float CompressionTimeMs = 0.0f;

    FCompressionResult() = default;
};

// 前向声明
class UMassRepresentationSubsystem;
struct FMassActorSpawnRequestHandle;

#include "PerformanceOptimizer.generated.h"

// 【协调器模式】ECacheLevel已集成到UCacheManager中

// 【协调器模式】压缩算法枚举和结果结构体已集成到UCacheManager中

// 【已删除】分层内存池枚举和结构 - 已被统一对象池替代

/**
 * LOD级别枚举
 */
UENUM(BlueprintType)
enum class ELODLevel : uint8
{
    High = 0,           // 高细节
    Medium = 1,         // 中等细节
    Low = 2,            // 低细节
    Culled = 3,         // 剔除（不渲染）
    MAX UMETA(Hidden)
};

/**
 * 性能统计信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPerformanceStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float FrameTime = 0.0f;             // 帧时间（毫秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float CPUUsage = 0.0f;              // CPU使用率（百分比）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float MemoryUsage = 0.0f;           // 内存使用量（MB）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 ActiveMapCells = 0;           // 活跃地图格子数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 LoadedChunks = 0;             // 已加载的地图块数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 CachedObjects = 0;            // 缓存对象数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 PooledObjects = 0;            // 对象池中对象数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float CacheHitRate = 0.0f;          // 缓存命中率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float UpdateTime = 0.0f;            // 更新时间（毫秒）

    FPerformanceStats()
    {
        FrameTime = 0.0f;
        MemoryUsage = 0.0f;
        ActiveMapCells = 0;
        CachedObjects = 0;
        PooledObjects = 0;
        CacheHitRate = 0.0f;
        UpdateTime = 0.0f;
    }
};

// 【协调器模式】FCacheEntry已集成到UCacheManager中

// 【协调器模式】预测缓存功能已集成到UCacheManager中

/**
 * 地图块信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FMapChunk
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    FIntPoint ChunkCoordinate = FIntPoint::ZeroValue; // 块坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    int32 ChunkSize = 32;               // 块大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    ELODLevel CurrentLOD = ELODLevel::Medium; // 当前LOD级别

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    TArray<FMapCell> MapCells;              // 格子数据

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    bool bIsLoaded = false;             // 是否已加载

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    bool bIsDirty = false;              // 是否需要更新

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    float LastUpdateTime = 0.0f;        // 最后更新时间

    FMapChunk()
    {
        ChunkCoordinate = FIntPoint::ZeroValue;
        ChunkSize = 32;
        CurrentLOD = ELODLevel::Medium;
        bIsLoaded = false;
        bIsDirty = false;
        LastUpdateTime = 0.0f;
    }
};

/**
 * 性能优化参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPerformanceOptimizationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableObjectPooling = true;   // 是否启用对象池

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableCaching = true;         // 是否启用缓存

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableChunking = true;        // 是否启用分块

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableLOD = true;             // 是否启用LOD

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableGPUCompute = true;      // 是否启用GPU计算

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnablePredictiveCache = true; // 是否启用智能预测缓存

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableTieredMemoryPool = true; // 是否启用分层内存池

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAsyncLoading = true;    // 是否启用异步流式加载

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAdaptiveOptimization = true; // 是否启用自适应优化

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableMemoryMapping = true;   // 是否启用内存映射文件系统

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 MaxCacheSize = 1024;          // 最大缓存大小（MB）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 ChunkSize = 32;               // 地图块大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance1 = 100.0f;        // LOD1距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance2 = 200.0f;        // LOD2距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance3 = 500.0f;        // LOD3距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float UpdateFrequency = 0.1f;       // 更新频率（秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableCompression = true;     // 是否启用压缩

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    double CacheExpirationTime = 300.0; // 缓存过期时间（秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 MaxLoadedChunks = 100;        // 最大加载块数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    TArray<float> LODDistances = {100.0f, 200.0f, 500.0f}; // LOD距离数组

    FPerformanceOptimizationParams()
    {
        bEnableObjectPooling = true;
        bEnableCaching = true;
        bEnableChunking = true;
        bEnableLOD = true;
        bEnableGPUCompute = true;
        bEnablePredictiveCache = true;
        bEnableTieredMemoryPool = true;
        bEnableAsyncLoading = true;
        bEnableAdaptiveOptimization = true;
        bEnableMemoryMapping = true;
        MaxCacheSize = 1024;
        ChunkSize = 32;
        LODDistance1 = 100.0f;
        LODDistance2 = 200.0f;
        LODDistance3 = 500.0f;
        UpdateFrequency = 0.1f;
        bEnableCompression = true;
        CacheExpirationTime = 300.0;
        MaxLoadedChunks = 100;
        LODDistances = {100.0f, 200.0f, 500.0f};
    }
};

// 前向声明
class UPerformanceOptimizer;

// 前向声明 - 使用引擎内置的ComputeFramework
class UComputeGraphComponent;

/**
 * 【异步流式加载】加载优先级枚举
 */
UENUM(BlueprintType)
enum class ELoadPriority : uint8
{
    Critical = 0,   // 玩家当前位置，最高优先级
    High = 1,       // 玩家附近区域，高优先级
    Normal = 2,     // 预加载区域，普通优先级
    Low = 3,        // 远距离预测，低优先级
    Background = 4  // 后台预加载，最低优先级
};

/**
 * 【异步流式加载】块加载请求结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FChunkLoadRequest
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Load Request")
    FIntPoint ChunkCoordinate = FIntPoint(0, 0);        // 块坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Load Request")
    ELoadPriority Priority = ELoadPriority::Normal;     // 加载优先级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Load Request")
    ELODLevel TargetLOD = ELODLevel::Medium;           // 目标LOD级别

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chunk Load Request")
    double RequestTime = 0.0;                           // 请求时间

    // 注意：TFunction不能用UPROPERTY，保持原样
    TFunction<void(const FMapChunk&)> OnLoadComplete;   // 加载完成回调

    FChunkLoadRequest()
    {
        ChunkCoordinate = FIntPoint(0, 0);
        Priority = ELoadPriority::Normal;
        TargetLOD = ELODLevel::Medium;
        RequestTime = 0.0;
    }
};

/**
 * 【异步流式加载升级】异步块加载器
 * 基于UE5的AsyncTask框架实现异步分块加载系统
 */
class GAME_API FAsyncChunkLoader
{
public:

private:
    // 【优先级队列】5个不同优先级的加载队列
    TArray<TQueue<FChunkLoadRequest>> PriorityQueues;

    // 【异步任务】当前正在执行的加载任务
    TArray<TFuture<FMapChunk>> LoadingTasks;

    // 【线程安全】加载队列互斥锁
    mutable FCriticalSection LoadQueueMutex;

    // 【统计信息】活跃加载任务计数
    mutable std::atomic<int32> ActiveLoadTasks{0};

    // 【配置参数】
    static constexpr int32 MAX_CONCURRENT_LOADS = 8;   // 最大并发加载数
    static constexpr double LOAD_TIMEOUT = 30.0;       // 加载超时时间（秒）

public:
    /**
     * 异步加载地图块
     * @param ChunkCoord 块坐标
     * @param Priority 加载优先级
     * @return 异步加载任务的Future
     */
    TFuture<FMapChunk> LoadChunkAsync(const FIntPoint& ChunkCoord, ELoadPriority Priority = ELoadPriority::Normal);

    // 【已删除】流式预加载功能已移除

    /**
     * 设置块的加载优先级
     * @param ChunkCoord 块坐标
     * @param Priority 新的优先级
     */
    void SetChunkPriority(const FIntPoint& ChunkCoord, ELoadPriority Priority);

    // 【已删除】处理加载队列功能已移除

    /**
     * 获取当前活跃的加载任务数量
     * @return 活跃任务数
     */
    int32 GetActiveLoadTaskCount() const { return ActiveLoadTasks.load(); }

    /**
     * 取消指定块的加载请求
     * @param ChunkCoord 要取消的块坐标
     */
    void CancelLoadRequest(const FIntPoint& ChunkCoord);

    // 【已删除】清理超时任务、初始化和关闭功能已移除
};



// 【协调器模式】内存映射缓存功能已集成到UCacheManager中

// 【协调器模式】自适应优化功能已集成到各Manager子系统中



// 【协调器模式】预测缓存功能已集成到UCacheManager中

/**
 * 性能优化器
 * 负责对象池管理、缓存系统、分块加载、LOD等性能优化功能
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API UPerformanceOptimizer : public UObject
{
    GENERATED_BODY()

public:
    UPerformanceOptimizer();

    // 【现代化升级】RAII析构函数，智能指针自动管理内存
    virtual ~UPerformanceOptimizer() = default;

    // ========== 主要优化函数 ==========
    
    /**
     * 初始化性能优化器
     * @param Params 优化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void Initialize(const FPerformanceOptimizationParams& Params);

    /**
     * 关闭性能优化器，清理所有资源
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void Shutdown();

    /**
     * 更新性能优化系统
     * @param DeltaTime 时间增量
     * @param ViewerPosition 观察者位置
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void UpdateOptimization(float DeltaTime, const FVector& ViewerPosition);

    // ========== 对象池管理 ==========
    
    /**
     * 【现代化升级】从对象池获取地图格子 - 使用智能指针
     * @return 地图格子智能指针
     */
    TSharedPtr<FMapCell> GetPooledMapCell();

    /**
     * 【现代化升级】将地图格子返回对象池 - 使用智能指针
     * @param Cell 地图格子智能指针
     */
    void ReturnPooledMapCell(TSharedPtr<FMapCell> Cell);

    // 【已删除】PreallocateObjectPool - 已被PreallocatePool<T>模板函数替代

    /**
     * 【现代化升级】清空对象池 - 智能指针自动管理内存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ClearObjectPool();

    // 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

public:
    // ========== 统一对象池系统 ==========

    /**
     * 【统一对象池】通用对象获取接口
     * @return 对象智能指针
     */
    template<typename T>
    TSharedPtr<T> GetPooledObject();

    /**
     * 【统一对象池】通用对象返回接口
     * @param Object 要返回的对象
     */
    template<typename T>
    void ReturnPooledObject(TSharedPtr<T> Object);

    /**
     * 【分层内存池升级】获取分层池FMapCell对象
     * @return 分层池对象智能指针
     */
    TSharedPtr<FMapCell> GetTieredPooledMapCell();

    /**
     * 【分层内存池升级】获取分层池AActor对象
     * @return 分层池对象智能指针
     */
    TSharedPtr<AActor> GetTieredPooledActor();

    /**
     * 【分层内存池升级】返回分层池FMapCell对象
     * @param Object 要返回的对象
     * @param PreferredTier 首选存储层级 (0=Hot, 1=Warm, 2=Cold)
     */
    void ReturnTieredPooledMapCell(TSharedPtr<FMapCell> Object, int32 PreferredTier = 1);

    /**
     * 【分层内存池升级】返回分层池AActor对象
     * @param Object 要返回的对象
     * @param PreferredTier 首选存储层级 (0=Hot, 1=Warm, 2=Cold)
     */
    void ReturnTieredPooledActor(TSharedPtr<AActor> Object, int32 PreferredTier = 1);

    // 【分层内存池升级】分层池访问器将在模板定义后实现



    /**
     * 【统一对象池】通用对象池预分配
     * @param Count 预分配数量
     */
    template<typename T>
    void PreallocatePool(int32 Count);

    /**
     * 【统一对象池】获取池统计信息
     * @return 池命中率
     */
    template<typename T>
    float GetPoolHitRate() const;

    /**
     * 【统一对象池】获取MapCell池命中率 - 蓝图接口
     * @return MapCell池命中率
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    float GetMapCellPoolHitRate() const;

    /**
     * 【统一对象池】获取Actor池命中率 - 蓝图接口
     * @return Actor池命中率
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    float GetActorPoolHitRate() const;

    /**
     * 【统一对象池】获取池活跃对象数量 - 蓝图接口
     * @return 活跃对象数量
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    int32 GetActiveObjectsCount() const;

    /**
     * 【统一对象池】获取池总分配次数 - 蓝图接口
     * @return 总分配次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    int32 GetTotalAllocationsCount() const;

private:
    // ========== 统一对象池辅助函数 ==========

    /**
     * 【统一对象池】获取指定类型的对象池引用
     */
    template<typename T>
    auto& GetPoolForType();

    template<typename T>
    const auto& GetPoolForType() const;

    /**
     * 【统一对象池】重置对象状态
     */
    template<typename T>
    void ResetObjectState(TSharedPtr<T> Object);

    /**
     * 【移动语义升级】完美转发模板函数 - 高效的容器操作
     * @param Container 目标容器
     * @param Args 要添加的元素参数
     */
    template<typename ContainerType, typename... Args>
    void EmplaceToContainer(ContainerType& Container, Args&&... Arguments)
    {
        Container.Emplace(Forward<Args>(Arguments)...);
    }

    // ========== 缓存系统 ==========
    
    /**
     * 缓存地图数据（拷贝版本）
     * @param Key 缓存键
     * @param Data 数据
     * @param Level 缓存级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void CacheMapData(const FString& Key, const TArray<FMapCell>& Data, ECacheLevel Level = ECacheLevel::L1_Memory);

    /**
     * 【移动语义升级】缓存地图数据（移动版本）- 避免大数组拷贝
     * @param Key 缓存键
     * @param Data 地图数据（将被移动）
     * @param Level 缓存级别
     */
    void CacheMapData(const FString& Key, TArray<FMapCell>&& Data, ECacheLevel Level = ECacheLevel::L1_Memory);

    /**
     * 获取缓存的地图数据
     * @param Key 缓存键
     * @param OutData 输出数据
     * @return 是否找到缓存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    bool GetCachedMapData(const FString& Key, TArray<FMapCell>& OutData);

    /**
     * 清理过期缓存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void CleanupExpiredCache();

public:
    // 【协调器模式】缓存条目访问已集成到UCacheManager中

    // ========== 分块系统 ==========
    
    /**
     * 将地图分割为块
     * @param MapData 地图数据
     * @param Width 地图宽度
     * @param Height 地图高度
     * @return 地图块数组
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    TArray<FMapChunk> ChunkifyMap(const TArray<FMapCell>& MapData, int32 Width, int32 Height);

    /**
     * 加载指定块
     * @param ChunkCoordinate 块坐标
     * @param LODLevel LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void LoadChunk(const FIntPoint& ChunkCoordinate, ELODLevel LODLevel);

    /**
     * 卸载指定块
     * @param ChunkCoordinate 块坐标
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void UnloadChunk(const FIntPoint& ChunkCoordinate);

    // ========== LOD系统 ==========
    
    /**
     * 根据距离计算LOD级别
     * @param Distance 距离
     * @return LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    ELODLevel CalculateLODLevel(float Distance) const;

    /**
     * 应用LOD到地图块
     * @param Chunk 地图块（输入输出）
     * @param TargetLOD 目标LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ApplyLODToChunk(FMapChunk& Chunk, ELODLevel TargetLOD);

    // ========== 性能监控 ==========
    
    /**
     * 获取性能统计信息
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    FPerformanceStats GetPerformanceStats() const;

    /**
     * 重置性能统计
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ResetPerformanceStats();

    // ========== 自适应优化 ==========

    /**
     * 【自适应优化升级】获取当前性能瓶颈类型
     * @return 瓶颈类型的字符串描述
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    FString GetCurrentBottleneck() const;

    /**
     * 【自适应优化升级】获取优化建议列表
     * @return 优化建议描述数组
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    TArray<FString> GetOptimizationSuggestions() const;

    /**
     * 【自适应优化升级】检查性能是否在改善
     * @return 性能是否在改善
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    bool IsPerformanceImproving() const;

    /**
     * 【自适应优化升级】手动触发参数优化
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    void TriggerAdaptiveOptimization();

    /**
     * 【自适应优化升级】重置性能历史记录
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    void ResetPerformanceHistory();





    // ========== UE5.6优化：对象池系统 ==========

    // 【协调器模式】UE5对象池功能已集成到UObjectPoolManager中

    // ========== 默认配置 ==========
    
    /**
     * 获取默认优化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    static FPerformanceOptimizationParams GetDefaultOptimizationParams();

private:
    // ========== 私有成员变量 ==========
    
    UPROPERTY()
    FPerformanceOptimizationParams OptimizationParams;

    // 【统一对象池】通用对象池系统 - 替代多个重复的池
    template<typename T>
    struct FUnifiedObjectPool
    {
        TArray<TSharedPtr<T>> Pool;
        std::atomic<int32> ActiveObjects{0};
        std::atomic<int32> TotalAllocations{0};
        std::atomic<int32> TotalReturns{0};
        FCriticalSection PoolMutex;

        // 池统计信息
        float GetHitRate() const noexcept
        {
            const int32 Total = TotalAllocations.load();
            const int32 Hits = TotalReturns.load();
            return Total > 0 ? static_cast<float>(Hits) / Total : 0.0f;
        }

        // 初始化池
        void Initialize(int32 InitialSize)
        {
            FScopeLock Lock(&PoolMutex);
            Pool.Reserve(InitialSize);
        }
    };

    // 【协调器模式】分层内存池功能已集成到UObjectPoolManager中


    // 【统一对象池】各类型对象池
    FUnifiedObjectPool<FMapCell> MapCellPool;
    FUnifiedObjectPool<AActor> ActorPool;  // 替代Mass Actor池

    // 【协调器模式】分层对象池实例已集成到UObjectPoolManager中

    // 【异步流式加载升级】异步块加载器
    FAsyncChunkLoader AsyncChunkLoader;

    // 【UE5.6优化】使用引擎内置的FStreamableManager进行资源流式加载
    TSharedPtr<FStreamableManager> StreamableManager;

    // 【UE5.6优化】使用引擎内置的FAssetRegistryModule进行资源管理
    IAssetRegistry* AssetRegistry;

    // 【GPU计算升级】GPU计算组件（使用引擎内置的ComputeFramework）
    UPROPERTY()
    TObjectPtr<UComputeGraphComponent> GPUComputeComponent;

    // 【协调器模式】缓存条目已集成到UCacheManager中

    // 【协调器模式】预测缓存功能已集成到UCacheManager中

    // 【协调器模式】自适应优化功能已集成到各Manager子系统中

    // 【协调器模式】内存映射缓存功能已集成到UCacheManager中

    // ========== 【协调器模式】Manager子系统引用 ==========

    /** 【协调器】对象池管理器引用 */
    UPROPERTY()
    TObjectPtr<UObjectPoolManager> ObjectPoolManager;

    /** 【协调器】缓存管理器引用 */
    UPROPERTY()
    TObjectPtr<UCacheManager> CacheManager;

    /** 【协调器】GPU计算管理器引用 */
    UPROPERTY()
    TObjectPtr<UGPUComputeManager> GPUComputeManager;

    UPROPERTY()
    TMap<FIntPoint, FMapChunk> LoadedChunks; // 已加载的块

    UPROPERTY()
    FPerformanceStats PerformanceStats; // 性能统计

    UPROPERTY()
    float LastUpdateTime = 0.0f;        // 上次更新时间

    // 【升级】新增高精度时间测量
    double LastFrameTime = 0.0;         // 上一帧时间（高精度）

    // 【并行处理升级】并行计算相关成员
    mutable std::atomic<bool> bParallelProcessingEnabled{true};  // 是否启用并行处理
    mutable std::atomic<int32> ParallelTaskCount{0};             // 当前并行任务数量

    // 【SIMD向量化升级】SIMD性能统计
    mutable std::atomic<int64> SIMDOperationsCount{0};           // SIMD操作计数
    mutable std::atomic<int64> ScalarOperationsCount{0};         // 标量操作计数

    // 【GPU计算升级】GPU计算状态
    mutable std::atomic<bool> bGPUComputeEnabled{false};         // GPU计算是否启用
    mutable std::atomic<int32> GPUTaskCount{0};                  // GPU任务计数

    // 【已删除】分层内存池成员变量 - 已被统一对象池替代

    // ========== 私有辅助函数 ==========
    
    // 更新性能统计
    void UpdatePerformanceStats();
    
    // 管理内存使用
    void ManageMemoryUsage();
    
    // 压缩缓存数据
    TArray<uint8> CompressData(const TArray<FMapCell>& Data);

    // 解压缓存数据
    TArray<FMapCell> DecompressData(const TArray<uint8>& CompressedData);

    // 【压缩算法升级】高级压缩算法函数

    // 【协调器模式】高级压缩功能已集成到UCacheManager中

    /**
     * 计算数据熵值 - 用于自适应压缩算法选择
     * @param Data 要分析的数据
     * @return 数据熵值 (0.0-1.0)
     */
    float CalculateDataEntropy(const TArray<FMapCell>& Data) const;

    /**
     * 检测重复模式 - 用于自适应压缩算法选择
     * @param Data 要分析的数据
     * @return 是否存在重复模式
     */
    bool HasRepeatingPatterns(const TArray<FMapCell>& Data) const;
    
    // 【移动语义升级】简化地图数据（用于LOD）- 统一使用移动版本
    TArray<FMapCell> SimplifyMapData(TArray<FMapCell>&& OriginalData, ELODLevel LODLevel);
    
    // 【现代化升级】计算两点间距离 - constexpr和noexcept优化
    static float CalculateDistance(const FIntPoint& A, const FIntPoint& B) noexcept;

    // 【现代化升级】获取块索引 - constexpr和noexcept优化
    static FIntPoint GetChunkCoordinate(int32 X, int32 Y, int32 ChunkSize) noexcept;

public:
    // ========== Mass系统对象池管理 ==========

    /**
     * 从Mass系统对象池获取或创建Actor
     * @param World 世界对象
     * @param ActorClass Actor类型
     * @param Transform Actor变换
     * @param ActorID Actor标识符
     * @return 获取或创建的Actor
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    AActor* GetOrCreateActorFromPool(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID);

    /**
     * 将Actor释放回Mass系统对象池
     * @param World 世界对象
     * @param Actor 要释放的Actor
     * @return 是否成功释放
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    bool ReleaseActorToPool(UWorld* World, AActor* Actor);

    // 【已删除】Mass Actor池函数 - 已被统一对象池替代

    /**
     * 【现代化升级】检查Actor是否来自对象池 - noexcept优化
     * @param Actor 要检查的Actor
     * @return 是否来自对象池
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    bool IsActorFromPool(AActor* Actor) const noexcept;

public:
    // ========== 并行处理控制 ==========

    /**
     * 【并行处理升级】启用/禁用并行处理
     * @param bEnabled 是否启用并行处理
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    void SetParallelProcessingEnabled(bool bEnabled) noexcept;

    /**
     * 【并行处理升级】检查并行处理是否启用
     * @return 是否启用并行处理
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    bool IsParallelProcessingEnabled() const noexcept;

    /**
     * 【并行处理升级】获取当前并行任务数量
     * @return 当前并行任务数量
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    int32 GetParallelTaskCount() const noexcept;

public:
    // ========== SIMD向量化优化 ==========

    /**
     * 【SIMD向量化升级】批量距离计算 - 使用SIMD指令
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances) noexcept;

    /**
     * 【GPU+SIMD混合优化】智能批量距离计算 - 根据数据量选择最优计算方式
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch_Hybrid(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances
    ) noexcept;

    /**
     * 【GPU计算升级】批量距离计算 - 使用GPU加速
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch_GPU(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances
    ) noexcept;

    /**
     * 【GPU计算升级】批量LOD计算 - 使用GPU加速
     * @param Chunks 地图块数组
     * @param ViewerPosition 观察者位置
     * @param OutLODLevels 输出LOD级别数组
     */
    void CalculateLODBatch_GPU(
        const TArray<FMapChunk>& Chunks,
        const FVector& ViewerPosition,
        TArray<ELODLevel>& OutLODLevels
    ) noexcept;

    /**
     * 【GPU计算升级】创建GPU计算回调
     * @param ChunkPtrs 地图块指针数组
     * @param ViewerPosition 观察者位置
     * @return GPU计算完成回调
     */
    FSimpleDelegate CreateGPULODCallback(
        const TArray<FMapChunk*>& ChunkPtrs,
        const FVector& ViewerPosition
    );

    /**
     * 【SIMD向量化升级】批量处理地图格子 - 使用SIMD指令
     * @param MapCells 地图格子数组
     * @param SimplificationFactor 简化因子
     */
    void ProcessMapCellsBatch(TArray<FMapCell>& MapCells, float SimplificationFactor) noexcept;

    /**
     * 【SIMD向量化升级】内存对齐的数据拷贝 - 使用SIMD优化
     * @param Source 源数据
     * @param Destination 目标数据
     */
    void CopyMapCellsAligned(const TArray<FMapCell>& Source, TArray<FMapCell>& Destination) noexcept;

    /**
     * 【SIMD向量化升级】获取SIMD操作统计
     * @return SIMD操作次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    int64 GetSIMDOperationsCount() const noexcept;

    /**
     * 【SIMD向量化升级】获取标量操作统计
     * @return 标量操作次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    int64 GetScalarOperationsCount() const noexcept;

    /**
     * 【SIMD向量化升级】获取SIMD效率比例
     * @return SIMD操作占总操作的比例 (0.0-1.0)
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    float GetSIMDEfficiencyRatio() const noexcept;

    // ========== 【压缩系统】私有压缩方法 ==========

    /**
     * 【自适应压缩】使用自适应算法压缩数据
     * @param Data 要压缩的原始数据
     * @return 压缩结果，包含压缩后的数据和统计信息
     */
    FCompressionResult CompressAdaptive(const TArray<FMapCell>& Data);

    /**
     * 【高级解压】使用指定算法解压数据
     * @param CompressedData 压缩后的数据
     * @param Algorithm 压缩算法
     * @return 解压后的原始数据
     */
    TArray<FMapCell> DecompressAdvanced(const TArray<uint8>& CompressedData, ECompressionAlgorithm Algorithm);

    /**
     * 【压缩算法选择】根据数据特征选择最佳压缩算法
     * @param Data 要分析的数据
     * @return 推荐的压缩算法
     */
    ECompressionAlgorithm SelectOptimalCompressionAlgorithm(const TArray<FMapCell>& Data);

    // 【已删除】分层内存池函数 - 已被统一对象池替代，避免重复实现
};
