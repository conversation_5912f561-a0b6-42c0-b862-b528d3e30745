#pragma once

#include "CoreMinimal.h"
#include "HAL/CriticalSection.h"
#include "Containers/LockFreeList.h"
#include "HAL/PlatformMemory.h"
#include "Templates/SharedPointer.h"
#include "Compression/OodleDataCompression.h"

/**
 * 【优化的分层对象池】
 * 
 * 统一的对象池系统，使用三层存储架构（Hot/Warm/Cold）。
 * 使用UE5.6的无锁数据结构提高并发性能，实现热/温/冷三层内存管理。
 * 
 * 设计原则：
 * - 热池使用无锁数据结构，获得最佳性能
 * - 温池使用互斥锁保护，平衡性能和安全性
 * - 冷池支持压缩存储，最大化内存利用率
 * - 使用缓存行对齐优化，避免伪共享
 * - 支持智能层间对象移动
 */
template<typename T>
class FOptimizedTieredObjectPool
{
public:
    /**
     * 【层级枚举】定义三层存储架构
     */
    enum class ETier : uint8
    {
        Hot = 0,    // 热池：无锁，最高性能，CPU缓存友好
        Warm = 1,   // 温池：低锁，中等性能，普通内存存储
        Cold = 2    // 冷池：压缩存储，最大容量，低频访问
    };

    /**
     * 【构造函数】初始化优化的分层对象池
     * 
     * @param HotSize 热池初始大小
     * @param WarmSize 温池初始大小
     * @param ColdSize 冷池初始大小
     */
    FOptimizedTieredObjectPool(int32 HotSize = 50, int32 WarmSize = 200, int32 ColdSize = 500)
        : MaxHotPoolSize(HotSize)
        , MaxWarmPoolSize(WarmSize)
        , MaxColdPoolSize(ColdSize)
        , HotPoolSize(0)
        , WarmPoolSize(0)
        , ColdPoolSize(0)
        , HotPoolHits(0)
        , WarmPoolHits(0)
        , ColdPoolHits(0)
        , TotalAllocations(0)
        , LastOptimizationTime(0.0)
    {
        // 【预分配】预分配热池对象以减少运行时分配
        for (int32 i = 0; i < HotSize / 2; ++i)
        {
            TSharedPtr<T, ESPMode::ThreadSafe> NewObject = MakeShared<T>();
            HotPool.Push(NewObject);
            HotPoolSize.fetch_add(1);
        }
        
        LastOptimizationTime = FPlatformTime::Seconds();
    }

    /**
     * 【析构函数】清理所有池
     */
    ~FOptimizedTieredObjectPool()
    {
        ClearAll();
    }

    /**
     * 【核心接口】从池中获取对象
     * 
     * 智能分层获取策略：
     * 1. 优先从热池获取（无锁，最快）
     * 2. 热池为空时从温池获取（低锁）
     * 3. 温池也为空时从冷池获取并解压（高延迟）
     * 4. 所有池都为空时创建新对象
     * 
     * @return 池化对象的智能指针
     */
    TSharedPtr<T, ESPMode::ThreadSafe> GetObject()
    {
        TotalAllocations.fetch_add(1);

        // 【热池优先】首先尝试从热池获取（无锁操作）
        if (TSharedPtr<T, ESPMode::ThreadSafe> Object = HotPool.Pop())
        {
            HotPoolSize.fetch_sub(1);
            HotPoolHits.fetch_add(1);
            return Object;
        }

        // 【温池备选】热池为空时从温池获取
        {
            FScopeLock Lock(&WarmPoolMutex);
            if (WarmPool.Num() > 0)
            {
                TSharedPtr<T, ESPMode::ThreadSafe> Object = WarmPool.Pop();
                WarmPoolSize.fetch_sub(1);
                WarmPoolHits.fetch_add(1);
                return Object;
            }
        }

        // 【冷池最后】温池也为空时从冷池获取
        {
            FScopeLock Lock(&ColdPoolMutex);
            if (ColdPool.Num() > 0)
            {
                // 【解压缩】从压缩数据恢复对象
                TArray<uint8> CompressedData = ColdPool.Pop();
                TSharedPtr<T, ESPMode::ThreadSafe> Object = DecompressObject(CompressedData);
                
                if (Object.IsValid())
                {
                    ColdPoolSize.fetch_sub(1);
                    ColdPoolHits.fetch_add(1);
                    return Object;
                }
            }
        }

        // 【创建新对象】所有池都为空时创建新对象
        return MakeShared<T>();
    }

    /**
     * 【核心接口】将对象归还到池中
     * 
     * 智能分层存储策略：
     * 1. 根据池容量和访问模式选择存储层级
     * 2. 热池优先，但有容量限制
     * 3. 温池作为缓冲，容量较大
     * 4. 冷池压缩存储，容量最大
     * 
     * @param Object 要归还的对象
     * @param PreferredTier 首选存储层级
     */
    void ReturnObject(TSharedPtr<T, ESPMode::ThreadSafe> Object, ETier PreferredTier = ETier::Warm)
    {
        if (!Object.IsValid())
        {
            return;
        }

        // 【智能分层决策】根据首选层级和容量情况决定存储位置
        switch (PreferredTier)
        {
            case ETier::Hot:
            {
                // 【热池存储】优先存储到热池
                const int32 CurrentHotSize = HotPoolSize.load();
                if (CurrentHotSize < MaxHotPoolSize)
                {
                    HotPool.Push(Object);
                    HotPoolSize.fetch_add(1);
                    return;
                }
                // 热池满了，降级到温池
                [[fallthrough]];
            }
            case ETier::Warm:
            {
                // 【温池存储】存储到温池
                FScopeLock Lock(&WarmPoolMutex);
                const int32 CurrentWarmSize = WarmPoolSize.load();
                if (CurrentWarmSize < MaxWarmPoolSize)
                {
                    WarmPool.Push(Object);
                    WarmPoolSize.fetch_add(1);
                    return;
                }
                // 温池满了，降级到冷池
                [[fallthrough]];
            }
            case ETier::Cold:
            {
                // 【冷池存储】压缩存储到冷池
                FScopeLock Lock(&ColdPoolMutex);
                const int32 CurrentColdSize = ColdPoolSize.load();
                if (CurrentColdSize < MaxColdPoolSize)
                {
                    TArray<uint8> CompressedData = CompressObject(*Object);
                    if (CompressedData.Num() > 0)
                    {
                        ColdPool.Push(CompressedData);
                        ColdPoolSize.fetch_add(1);
                        return;
                    }
                }
                // 冷池也满了，直接丢弃对象（让智能指针自动析构）
                break;
            }
        }
    }

    /**
     * 【统计接口】获取指定层级的命中率
     * 
     * @param Tier 要查询的层级
     * @return 命中率（0.0f - 1.0f）
     */
    float GetTierHitRate(ETier Tier) const
    {
        const int32 TotalAllocs = TotalAllocations.load();
        if (TotalAllocs == 0)
        {
            return 0.0f;
        }

        switch (Tier)
        {
            case ETier::Hot:
                return static_cast<float>(HotPoolHits.load()) / TotalAllocs;
            case ETier::Warm:
                return static_cast<float>(WarmPoolHits.load()) / TotalAllocs;
            case ETier::Cold:
                return static_cast<float>(ColdPoolHits.load()) / TotalAllocs;
            default:
                return 0.0f;
        }
    }

    /**
     * 【统计接口】获取总命中率
     * 
     * @return 总命中率（0.0f - 1.0f）
     */
    float GetTotalHitRate() const
    {
        const int32 TotalAllocs = TotalAllocations.load();
        if (TotalAllocs == 0)
        {
            return 0.0f;
        }

        const int32 TotalHits = HotPoolHits.load() + WarmPoolHits.load() + ColdPoolHits.load();
        return static_cast<float>(TotalHits) / TotalAllocs;
    }

    /**
     * 【统计接口】获取各层级的对象数量
     * 
     * @param OutHotCount 热池对象数量
     * @param OutWarmCount 温池对象数量
     * @param OutColdCount 冷池对象数量
     */
    void GetPoolSizes(int32& OutHotCount, int32& OutWarmCount, int32& OutColdCount) const
    {
        OutHotCount = HotPoolSize.load();
        OutWarmCount = WarmPoolSize.load();
        OutColdCount = ColdPoolSize.load();
    }

    /**
     * 【优化接口】自动优化池大小
     * 
     * 根据命中率和使用模式自动调整各层级的大小限制。
     * 定期调用以保持最优性能。
     */
    void OptimizePoolSizes()
    {
        const double CurrentTime = FPlatformTime::Seconds();
        const double TimeSinceLastOptimization = CurrentTime - LastOptimizationTime;
        
        // 【优化频率控制】每60秒优化一次
        if (TimeSinceLastOptimization < 60.0)
        {
            return;
        }

        const int32 TotalAllocs = TotalAllocations.load();
        if (TotalAllocs < 100) // 样本太少，不进行优化
        {
            return;
        }

        // 【热池优化】如果热池命中率高，增加热池大小
        const float HotHitRate = GetTierHitRate(ETier::Hot);
        if (HotHitRate > 0.7f && MaxHotPoolSize < 200)
        {
            MaxHotPoolSize = FMath::Min(MaxHotPoolSize + 10, 200);
        }
        else if (HotHitRate < 0.3f && MaxHotPoolSize > 20)
        {
            MaxHotPoolSize = FMath::Max(MaxHotPoolSize - 10, 20);
        }

        // 【温池优化】根据温池使用情况调整
        const float WarmHitRate = GetTierHitRate(ETier::Warm);
        if (WarmHitRate > 0.5f && MaxWarmPoolSize < 500)
        {
            MaxWarmPoolSize = FMath::Min(MaxWarmPoolSize + 20, 500);
        }
        else if (WarmHitRate < 0.1f && MaxWarmPoolSize > 50)
        {
            MaxWarmPoolSize = FMath::Max(MaxWarmPoolSize - 20, 50);
        }

        LastOptimizationTime = CurrentTime;
    }

    /**
     * 【管理接口】清理所有池
     */
    void ClearAll()
    {
        // 清理热池（无锁）
        while (TSharedPtr<T, ESPMode::ThreadSafe> Object = HotPool.Pop())
        {
            // 对象自动析构
        }
        HotPoolSize.store(0);

        // 清理温池
        {
            FScopeLock Lock(&WarmPoolMutex);
            WarmPool.Empty();
            WarmPoolSize.store(0);
        }

        // 清理冷池
        {
            FScopeLock Lock(&ColdPoolMutex);
            ColdPool.Empty();
            ColdPoolSize.store(0);
        }

        // 重置统计
        HotPoolHits.store(0);
        WarmPoolHits.store(0);
        ColdPoolHits.store(0);
        TotalAllocations.store(0);
    }

private:
    /** 【热池】使用无锁列表，最频繁访问，CPU缓存友好 */
    TLockFreePointerListUnordered<T, PLATFORM_CACHE_LINE_SIZE> HotPool;
    
    /** 【温池】使用互斥锁保护的数组，中等频率访问 */
    TArray<TSharedPtr<T, ESPMode::ThreadSafe>> WarmPool;
    FCriticalSection WarmPoolMutex;
    
    /** 【冷池】压缩存储，最大容量，最低频率访问 */
    TArray<TArray<uint8>> ColdPool;
    FCriticalSection ColdPoolMutex;

    /** 【配置参数】各池最大大小（可动态调整） */
    TAtomic<int32> MaxHotPoolSize;
    TAtomic<int32> MaxWarmPoolSize;
    TAtomic<int32> MaxColdPoolSize;

    /** 【统计数据】各池当前大小（缓存行对齐，避免伪共享） */
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> HotPoolSize;
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> WarmPoolSize;
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> ColdPoolSize;

    /** 【统计数据】各池命中次数 */
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> HotPoolHits;
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> WarmPoolHits;
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> ColdPoolHits;
    alignas(PLATFORM_CACHE_LINE_SIZE) TAtomic<int32> TotalAllocations;

    /** 【优化数据】上次优化时间 */
    TAtomic<double> LastOptimizationTime;

    /**
     * 【内部方法】压缩对象到字节数组
     * 
     * @param Object 要压缩的对象
     * @return 压缩后的字节数组
     */
    TArray<uint8> CompressObject(const T& Object) const
    {
        // 【序列化】将对象序列化为字节数组
        TArray<uint8> RawData;
        FMemoryWriter Writer(RawData);
        
        // 简化实现：直接复制对象内存
        // 实际应该使用UE5的序列化系统
        const int32 ObjectSize = sizeof(T);
        RawData.SetNumUninitialized(ObjectSize);
        FMemory::Memcpy(RawData.GetData(), &Object, ObjectSize);

        // 【压缩】使用UE5内置压缩
        TArray<uint8> CompressedData;
        const int32 MaxCompressedSize = FCompression::CompressMemoryBound(NAME_Zlib, RawData.Num());
        CompressedData.SetNumUninitialized(MaxCompressedSize);
        
        int32 CompressedSize = MaxCompressedSize;
        const bool bSuccess = FCompression::CompressMemory(
            NAME_Zlib,
            CompressedData.GetData(),
            CompressedSize,
            RawData.GetData(),
            RawData.Num()
        );
        
        if (bSuccess && CompressedSize < RawData.Num())
        {
            CompressedData.SetNum(CompressedSize);
            return CompressedData;
        }
        
        // 压缩失败或无效果，返回原始数据
        return RawData;
    }

    /**
     * 【内部方法】从压缩数据恢复对象
     * 
     * @param CompressedData 压缩的字节数组
     * @return 恢复的对象智能指针
     */
    TSharedPtr<T, ESPMode::ThreadSafe> DecompressObject(const TArray<uint8>& CompressedData) const
    {
        if (CompressedData.Num() == 0)
        {
            return nullptr;
        }

        // 【解压缩】尝试解压数据
        TArray<uint8> RawData;
        int32 UncompressedSize = sizeof(T) * 2; // 估算大小
        RawData.SetNumUninitialized(UncompressedSize);
        
        const bool bSuccess = FCompression::UncompressMemory(
            NAME_Zlib,
            RawData.GetData(),
            UncompressedSize,
            CompressedData.GetData(),
            CompressedData.Num()
        );
        
        if (bSuccess && UncompressedSize >= sizeof(T))
        {
            // 【反序列化】从字节数组恢复对象
            TSharedPtr<T, ESPMode::ThreadSafe> Object = MakeShared<T>();
            FMemory::Memcpy(Object.Get(), RawData.GetData(), sizeof(T));
            return Object;
        }
        
        // 解压失败，创建新对象
        return MakeShared<T>();
    }
};
