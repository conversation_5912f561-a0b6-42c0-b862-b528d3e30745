#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

// 【并行处理升级】并行计算支持
#include "HAL/ThreadSafeBool.h"
#include <atomic>

// 【GPU计算升级】GPU加速计算支持
#include "ComputeFramework/ComputeGraphComponent.h"

// 【内存池高级优化】分层内存池支持
#include "HAL/CriticalSection.h"
#include "Containers/LockFreeList.h"
#include <memory_resource>

// 【协调器模式】Manager子系统前向声明
class UObjectPoolManager;
class UCacheManager;
class UGPUComputeManager;

// 前向声明
class UMassRepresentationSubsystem;
struct FMassActorSpawnRequestHandle;

#include "PerformanceOptimizer.generated.h"

/**
 * 缓存级别枚举
 */
UENUM(BlueprintType)
enum class ECacheLevel : uint8
{
    L1_Memory = 0,      // 内存缓存（最快）
    L2_Compressed,      // 压缩缓存（中等）
    L3_Disk,           // 磁盘缓存（最慢）
    MAX UMETA(Hidden)
};

/**
 * 【压缩算法升级】高级压缩算法枚举
 */
UENUM(BlueprintType)
enum class ECompressionAlgorithm : uint8
{
    None = 0,           // 不压缩
    Zlib = 1,           // 现有Zlib算法
    LZ4 = 2,            // 快速压缩算法
    ZSTD = 3,           // 高压缩比算法
    Delta = 4,          // 差分压缩算法
    Adaptive = 5        // 自适应选择算法
};

/**
 * 【压缩算法升级】压缩结果结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FCompressionResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression Result")
    TArray<uint8> CompressedData;          // 压缩后的数据

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression Result")
    ECompressionAlgorithm Algorithm;       // 使用的压缩算法

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression Result")
    float CompressionRatio = 0.0f;         // 压缩率 (0.0-1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression Result")
    double CompressionTime = 0.0;          // 压缩耗时（秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression Result")
    bool bSuccess = false;                 // 压缩是否成功

    FCompressionResult()
    {
        Algorithm = ECompressionAlgorithm::None;
        CompressionRatio = 0.0f;
        CompressionTime = 0.0;
        bSuccess = false;
    }
};

// 【已删除】分层内存池枚举和结构 - 已被统一对象池替代

/**
 * LOD级别枚举
 */
UENUM(BlueprintType)
enum class ELODLevel : uint8
{
    LOD0_Highest = 0,   // 最高细节
    LOD1_High,          // 高细节
    LOD2_Medium,        // 中等细节
    LOD3_Low,           // 低细节
    LOD4_Lowest,        // 最低细节
    MAX UMETA(Hidden)
};

/**
 * 性能统计信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPerformanceStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float FrameTime = 0.0f;             // 帧时间（毫秒）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float MemoryUsage = 0.0f;           // 内存使用量（MB）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 ActiveMapCells = 0;           // 活跃地图格子数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 CachedObjects = 0;            // 缓存对象数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    int32 PooledObjects = 0;            // 对象池中对象数量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float CacheHitRate = 0.0f;          // 缓存命中率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Stats")
    float UpdateTime = 0.0f;            // 更新时间（毫秒）

    FPerformanceStats()
    {
        FrameTime = 0.0f;
        MemoryUsage = 0.0f;
        ActiveMapCells = 0;
        CachedObjects = 0;
        PooledObjects = 0;
        CacheHitRate = 0.0f;
        UpdateTime = 0.0f;
    }
};

/**
 * 缓存条目结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FCacheEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    FString Key = TEXT("");             // 缓存键

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    ECacheLevel Level = ECacheLevel::L1_Memory; // 缓存级别

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    float LastAccessTime = 0.0f;        // 最后访问时间

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    int32 AccessCount = 0;              // 访问次数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    int32 DataSize = 0;                 // 数据大小（字节）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    bool bIsCompressed = false;         // 是否压缩

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    ECompressionAlgorithm CompressionAlgorithm = ECompressionAlgorithm::None; // 压缩算法

    // 【修复压缩系统】添加实际数据存储字段
    TArray<uint8> RawData;              // 原始数据（未压缩）
    TArray<uint8> CompressedData;       // 压缩数据

    FCacheEntry()
    {
        Key = TEXT("");
        Level = ECacheLevel::L1_Memory;
        LastAccessTime = 0.0f;
        AccessCount = 0;
        DataSize = 0;
        bIsCompressed = false;
    }
};

// 【协调器模式】预测缓存条目已集成到UCacheManager中

/**
 * 地图块信息结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FMapChunk
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    FIntPoint ChunkCoordinate = FIntPoint::ZeroValue; // 块坐标

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    int32 ChunkSize = 32;               // 块大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    ELODLevel CurrentLOD = ELODLevel::LOD2_Medium; // 当前LOD级别

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    TArray<FMapCell> CellData;          // 格子数据

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    bool bIsLoaded = false;             // 是否已加载

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    bool bIsDirty = false;              // 是否需要更新

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Chunk")
    float LastUpdateTime = 0.0f;        // 最后更新时间

    FMapChunk()
    {
        ChunkCoordinate = FIntPoint::ZeroValue;
        ChunkSize = 32;
        CurrentLOD = ELODLevel::LOD2_Medium;
        bIsLoaded = false;
        bIsDirty = false;
        LastUpdateTime = 0.0f;
    }
};

/**
 * 性能优化参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPerformanceOptimizationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableObjectPooling = true;   // 是否启用对象池

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableCaching = true;         // 是否启用缓存

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableChunking = true;        // 是否启用分块

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableLOD = true;             // 是否启用LOD

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableGPUCompute = true;      // 是否启用GPU计算

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnablePredictiveCache = true; // 是否启用智能预测缓存

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableTieredMemoryPool = true; // 是否启用分层内存池

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAsyncLoading = true;    // 是否启用异步流式加载

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAdaptiveOptimization = true; // 是否启用自适应优化

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableMemoryMapping = true;   // 是否启用内存映射文件系统

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 MaxCacheSize = 1024;          // 最大缓存大小（MB）

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 ChunkSize = 32;               // 地图块大小

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance1 = 100.0f;        // LOD1距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance2 = 200.0f;        // LOD2距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float LODDistance3 = 500.0f;        // LOD3距离阈值

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float UpdateFrequency = 0.1f;       // 更新频率（秒）

    FPerformanceOptimizationParams()
    {
        bEnableObjectPooling = true;
        bEnableCaching = true;
        bEnableChunking = true;
        bEnableLOD = true;
        bEnableGPUCompute = true;
        bEnablePredictiveCache = true;
        bEnableTieredMemoryPool = true;
        bEnableAsyncLoading = true;
        bEnableAdaptiveOptimization = true;
        bEnableMemoryMapping = true;
        MaxCacheSize = 1024;
        ChunkSize = 32;
        LODDistance1 = 100.0f;
        LODDistance2 = 200.0f;
        LODDistance3 = 500.0f;
        UpdateFrequency = 0.1f;
    }
};

// 前向声明
class UPerformanceOptimizer;

// 【协调器模式】异步加载功能已被UE5.6内置的FStreamableManager和FAssetRegistryModule替代

// 【协调器模式】内存映射缓存功能已集成到UCacheManager中

// 【协调器模式】自适应优化功能已集成到各Manager子系统内部

// 【协调器模式】预测缓存功能已集成到UCacheManager中

/**
 * 性能优化器
 * 负责对象池管理、缓存系统、分块加载、LOD等性能优化功能
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API UPerformanceOptimizer : public UObject
{
    GENERATED_BODY()

public:
    UPerformanceOptimizer();

    // 【现代化升级】RAII析构函数，智能指针自动管理内存
    virtual ~UPerformanceOptimizer() = default;

    // 【移动语义升级】移动构造函数和移动赋值操作符
    UPerformanceOptimizer(UPerformanceOptimizer&& Other) noexcept = default;
    UPerformanceOptimizer& operator=(UPerformanceOptimizer&& Other) noexcept = default;

    // 【现代化】禁用拷贝构造和拷贝赋值（UObject通常不应该被拷贝）
    UPerformanceOptimizer(const UPerformanceOptimizer&) = delete;
    UPerformanceOptimizer& operator=(const UPerformanceOptimizer&) = delete;

    // ========== 主要优化函数 ==========
    
    /**
     * 初始化性能优化器
     * @param Params 优化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void Initialize(const FPerformanceOptimizationParams& Params);

    /**
     * 【集成验证】验证Manager子系统集成完整性
     *
     * 测试三个Manager子系统的基本功能和协作能力
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void VerifyManagerIntegration();

    /**
     * 【回退测试】测试错误处理和回退机制
     *
     * 验证Manager不可用时的回退处理是否正常
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void TestErrorHandlingAndFallback();

    /**
     * 【线程安全测试】测试Manager子系统线程安全性
     *
     * 验证多线程环境下Manager子系统的安全性
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void TestThreadSafety();

    /**
     * 关闭性能优化器，清理所有资源
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void Shutdown();

    /**
     * 更新性能优化系统
     * @param DeltaTime 时间增量
     * @param ViewerPosition 观察者位置
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void UpdateOptimization(float DeltaTime, const FVector& ViewerPosition);

    // ========== 对象池管理 ==========
    
    /**
     * 【智能指针标准化】从对象池获取地图格子 - 使用线程安全的智能指针
     * @return 地图格子线程安全智能指针
     */
    TSharedPtr<FMapCell, ESPMode::ThreadSafe> GetPooledMapCell();

    /**
     * 【智能指针标准化】将地图格子返回对象池 - 使用线程安全的智能指针
     * @param Cell 地图格子线程安全智能指针
     */
    void ReturnPooledMapCell(TSharedPtr<FMapCell, ESPMode::ThreadSafe> Cell);

    // 【已删除】PreallocateObjectPool - 已被PreallocatePool<T>模板函数替代

    /**
     * 【现代化升级】清空对象池 - 智能指针自动管理内存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ClearObjectPool();

    // 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

public:
    // ========== 统一对象池系统 ==========

    /**
     * 【智能指针标准化】通用对象获取接口 - 使用线程安全的智能指针
     * @return 对象线程安全智能指针
     */
    template<typename T>
    TSharedPtr<T, ESPMode::ThreadSafe> GetPooledObject();

    /**
     * 【智能指针标准化】通用对象返回接口 - 使用线程安全的智能指针
     * @param Object 要返回的对象线程安全智能指针
     */
    template<typename T>
    void ReturnPooledObject(TSharedPtr<T, ESPMode::ThreadSafe> Object);

    /**
     * 【智能指针标准化】获取分层池对象 - 使用线程安全的智能指针
     * @return 分层池对象线程安全智能指针
     */
    template<typename T>
    TSharedPtr<T, ESPMode::ThreadSafe> GetTieredPooledObject();

    // 【协调器模式】分层池对象功能已集成到UObjectPoolManager中

    /**
     * 【统一对象池】通用对象池预分配
     * @param Count 预分配数量
     */
    template<typename T>
    void PreallocatePool(int32 Count);

    /**
     * 【统一对象池】获取池统计信息
     * @return 池命中率
     */
    template<typename T>
    float GetPoolHitRate() const;

    /**
     * 【统一对象池】获取MapCell池命中率 - 蓝图接口
     * @return MapCell池命中率
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    float GetMapCellPoolHitRate() const;

    /**
     * 【统一对象池】获取Actor池命中率 - 蓝图接口
     * @return Actor池命中率
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    float GetActorPoolHitRate() const;

    /**
     * 【统一对象池】获取池活跃对象数量 - 蓝图接口
     * @return 活跃对象数量
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    int32 GetActiveObjectsCount() const;

    /**
     * 【统一对象池】获取池总分配次数 - 蓝图接口
     * @return 总分配次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Unified Pool")
    int32 GetTotalAllocationsCount() const;

private:
    // ========== 统一对象池辅助函数 ==========

    /**
     * 【统一对象池】获取指定类型的对象池引用
     */
    template<typename T>
    auto& GetPoolForType();

    template<typename T>
    const auto& GetPoolForType() const;

    /**
     * 【智能指针标准化】重置对象状态 - 使用线程安全的智能指针
     */
    template<typename T>
    void ResetObjectState(TSharedPtr<T, ESPMode::ThreadSafe> Object);

    /**
     * 【移动语义升级】完美转发模板函数 - 高效的容器操作
     * @param Container 目标容器
     * @param Args 要添加的元素参数
     */
    template<typename ContainerType, typename... Args>
    void EmplaceToContainer(ContainerType& Container, Args&&... Arguments)
    {
        Container.Emplace(Forward<Args>(Arguments)...);
    }

    // ========== 缓存系统 ==========
    
    /**
     * 缓存地图数据（拷贝版本）
     * @param Key 缓存键
     * @param Data 数据
     * @param Level 缓存级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void CacheMapData(const FString& Key, const TArray<FMapCell>& Data, ECacheLevel Level = ECacheLevel::L1_Memory);

    /**
     * 【移动语义升级】缓存地图数据（移动版本）- 避免大数组拷贝
     * @param Key 缓存键
     * @param Data 地图数据（将被移动）
     * @param Level 缓存级别
     */
    void CacheMapData(const FString& Key, TArray<FMapCell>&& Data, ECacheLevel Level = ECacheLevel::L1_Memory);

    /**
     * 获取缓存的地图数据
     * @param Key 缓存键
     * @param OutData 输出数据
     * @return 是否找到缓存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    bool GetCachedMapData(const FString& Key, TArray<FMapCell>& OutData);

    /**
     * 清理过期缓存
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void CleanupExpiredCache();

    // ========== 分块系统 ==========
    
    /**
     * 将地图分割为块
     * @param MapData 地图数据
     * @param Width 地图宽度
     * @param Height 地图高度
     * @return 地图块数组
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    TArray<FMapChunk> ChunkifyMap(const TArray<FMapCell>& MapData, int32 Width, int32 Height);

    /**
     * 加载指定块
     * @param ChunkCoordinate 块坐标
     * @param LODLevel LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void LoadChunk(const FIntPoint& ChunkCoordinate, ELODLevel LODLevel);

    /**
     * 卸载指定块
     * @param ChunkCoordinate 块坐标
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void UnloadChunk(const FIntPoint& ChunkCoordinate);

    // ========== LOD系统 ==========
    
    /**
     * 根据距离计算LOD级别
     * @param Distance 距离
     * @return LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    ELODLevel CalculateLODLevel(float Distance) const;

    /**
     * 应用LOD到地图块
     * @param Chunk 地图块（输入输出）
     * @param TargetLOD 目标LOD级别
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ApplyLODToChunk(FMapChunk& Chunk, ELODLevel TargetLOD);

    // ========== 性能监控 ==========
    
    /**
     * 获取性能统计信息
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    FPerformanceStats GetPerformanceStats() const;

    /**
     * 重置性能统计
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    void ResetPerformanceStats();

    // ========== 自适应优化 ==========

    /**
     * 【自适应优化升级】获取当前性能瓶颈类型
     * @return 瓶颈类型的字符串描述
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    FString GetCurrentBottleneck() const;

    /**
     * 【自适应优化升级】获取优化建议列表
     * @return 优化建议描述数组
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    TArray<FString> GetOptimizationSuggestions() const;

    /**
     * 【自适应优化升级】检查性能是否在改善
     * @return 性能是否在改善
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    bool IsPerformanceImproving() const;

    /**
     * 【自适应优化升级】手动触发参数优化
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    void TriggerAdaptiveOptimization();

    /**
     * 【自适应优化升级】重置性能历史记录
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Adaptive")
    void ResetPerformanceHistory();

    // ========== 默认配置 ==========
    
    /**
     * 获取默认优化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer")
    static FPerformanceOptimizationParams GetDefaultOptimizationParams();

private:
    // ========== 【协调器模式】Manager子系统引用 ==========

    /** 【协调器】对象池管理器引用 */
    UPROPERTY()
    TObjectPtr<UObjectPoolManager> ObjectPoolManager;

    /** 【协调器】缓存管理器引用 */
    UPROPERTY()
    TObjectPtr<UCacheManager> CacheManager;

    /** 【协调器】GPU计算管理器引用 */
    UPROPERTY()
    TObjectPtr<UGPUComputeManager> GPUComputeManager;

    /** 【向后兼容】优化参数 - 保留用于配置各Manager */
    UPROPERTY()
    FPerformanceOptimizationParams OptimizationParams;


    // 【协调器模式】所有直接实现已移除，改为委托给专门的Manager子系统
    // 原有的FTieredObjectPool、FAsyncChunkLoader、FPredictiveCache等
    // 已被UObjectPoolManager、UCacheManager、UGPUComputeManager替代

    // 【协调器模式】预测缓存和内存映射功能已集成到UCacheManager中

    UPROPERTY()
    TMap<FIntPoint, FMapChunk> LoadedChunks; // 已加载的块

    UPROPERTY()
    FPerformanceStats PerformanceStats; // 性能统计

    UPROPERTY()
    float LastUpdateTime = 0.0f;        // 上次更新时间

    // 【升级】新增高精度时间测量
    double LastFrameTime = 0.0;         // 上一帧时间（高精度）

    // 【并行处理升级】并行计算相关成员
    mutable std::atomic<bool> bParallelProcessingEnabled{true};  // 是否启用并行处理
    mutable std::atomic<int32> ParallelTaskCount{0};             // 当前并行任务数量

    // 【SIMD向量化升级】SIMD性能统计
    mutable std::atomic<int64> SIMDOperationsCount{0};           // SIMD操作计数
    mutable std::atomic<int64> ScalarOperationsCount{0};         // 标量操作计数

    // 【GPU计算升级】GPU计算状态
    mutable std::atomic<bool> bGPUComputeEnabled{false};         // GPU计算是否启用
    mutable std::atomic<int32> GPUTaskCount{0};                  // GPU任务计数

    // 【已删除】分层内存池成员变量 - 已被统一对象池替代

    // ========== 私有辅助函数 ==========
    
    // 更新性能统计
    void UpdatePerformanceStats();
    
    // 管理内存使用
    void ManageMemoryUsage();
    
    // 压缩缓存数据
    TArray<uint8> CompressData(const TArray<FMapCell>& Data);

    // 解压缓存数据
    TArray<FMapCell> DecompressData(const TArray<uint8>& CompressedData);

    // 【压缩算法升级】高级压缩算法函数

    // 【协调器模式】压缩功能已委托给UCacheManager，删除重复实现
    
    // 【移动语义升级】简化地图数据（用于LOD）- 统一使用移动版本
    TArray<FMapCell> SimplifyMapData(TArray<FMapCell>&& OriginalData, ELODLevel LODLevel);
    
    // 【现代化升级】计算两点间距离 - constexpr和noexcept优化
    static constexpr float CalculateDistance(const FIntPoint& A, const FIntPoint& B) noexcept;

    // 【现代化升级】获取块索引 - constexpr和noexcept优化
    static constexpr FIntPoint GetChunkCoordinate(int32 X, int32 Y, int32 ChunkSize) noexcept;

public:
    // ========== Mass系统对象池管理 ==========

    /**
     * 【智能指针标准化】从Mass系统对象池获取或创建Actor - 使用UE5.6推荐的TObjectPtr
     * @param World 世界对象
     * @param ActorClass Actor类型
     * @param Transform Actor变换
     * @param ActorID Actor标识符
     * @return 获取或创建的Actor智能指针
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    TObjectPtr<AActor> GetOrCreateActorFromPool(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID);

    /**
     * 【向后兼容】从对象池获取Actor - 废弃接口，请使用新的智能指针版本
     * @param World 世界对象
     * @param ActorClass Actor类型
     * @param Transform Actor变换
     * @param ActorID Actor标识符
     * @return 获取或创建的Actor原始指针
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool", meta = (DeprecatedFunction, DeprecationMessage = "Use GetOrCreateActorFromPool() with TObjectPtr instead"))
    AActor* GetOrCreateActorFromPool_DEPRECATED(UWorld* World, TSubclassOf<AActor> ActorClass, const FTransform& Transform, const FString& ActorID);

    /**
     * 【智能指针标准化】将Actor释放回Mass系统对象池 - 使用UE5.6推荐的TObjectPtr
     * @param World 世界对象
     * @param Actor 要释放的Actor智能指针
     * @return 是否成功释放
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    bool ReleaseActorToPool(UWorld* World, TObjectPtr<AActor> Actor);

    /**
     * 【向后兼容】释放Actor到对象池 - 废弃接口，请使用新的智能指针版本
     * @param World 世界对象
     * @param Actor 要释放的Actor原始指针
     * @return 是否成功释放
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool", meta = (DeprecatedFunction, DeprecationMessage = "Use ReleaseActorToPool() with TObjectPtr instead"))
    bool ReleaseActorToPool_DEPRECATED(UWorld* World, AActor* Actor);

    // 【已删除】Mass Actor池函数 - 已被统一对象池替代

    /**
     * 【智能指针标准化】检查Actor是否来自对象池 - 使用UE5.6推荐的TObjectPtr
     * @param Actor 要检查的Actor智能指针
     * @return 是否来自对象池
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool")
    bool IsActorFromPool(TObjectPtr<AActor> Actor) const noexcept;

    /**
     * 【向后兼容】检查Actor是否来自对象池 - 废弃接口，请使用新的智能指针版本
     * @param Actor 要检查的Actor原始指针
     * @return 是否来自对象池
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Object Pool", meta = (DeprecatedFunction, DeprecationMessage = "Use IsActorFromPool() with TObjectPtr instead"))
    bool IsActorFromPool_DEPRECATED(AActor* Actor) const noexcept;

public:
    // ========== 并行处理控制 ==========

    /**
     * 【并行处理升级】启用/禁用并行处理
     * @param bEnabled 是否启用并行处理
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    void SetParallelProcessingEnabled(bool bEnabled) noexcept;

    /**
     * 【并行处理升级】检查并行处理是否启用
     * @return 是否启用并行处理
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    bool IsParallelProcessingEnabled() const noexcept;

    /**
     * 【并行处理升级】获取当前并行任务数量
     * @return 当前并行任务数量
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|Parallel Processing")
    int32 GetParallelTaskCount() const noexcept;

public:
    // ========== SIMD向量化优化 ==========

    /**
     * 【SIMD向量化升级】批量距离计算 - 使用SIMD指令
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances) noexcept;

    /**
     * 【GPU+SIMD混合优化】智能批量距离计算 - 根据数据量选择最优计算方式
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch_Hybrid(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances
    ) noexcept;

    /**
     * 【GPU计算升级】批量距离计算 - 使用GPU加速
     * @param PointsA 起始点数组
     * @param PointsB 目标点数组
     * @param OutDistances 输出距离数组
     */
    void CalculateDistancesBatch_GPU(
        const TArray<FIntPoint>& PointsA,
        const TArray<FIntPoint>& PointsB,
        TArray<float>& OutDistances
    ) noexcept;

    /**
     * 【GPU计算升级】批量LOD计算 - 使用GPU加速
     * @param Chunks 地图块数组
     * @param ViewerPosition 观察者位置
     * @param OutLODLevels 输出LOD级别数组
     */
    void CalculateLODBatch_GPU(
        const TArray<FMapChunk>& Chunks,
        const FVector& ViewerPosition,
        TArray<ELODLevel>& OutLODLevels
    ) noexcept;

    /**
     * 【GPU计算升级】创建GPU计算回调
     * @param ChunkPtrs 地图块指针数组
     * @param ViewerPosition 观察者位置
     * @return GPU计算完成回调
     */
    FSimpleDelegate CreateGPULODCallback(
        const TArray<FMapChunk*>& ChunkPtrs,
        const FVector& ViewerPosition
    );

    /**
     * 【SIMD向量化升级】批量处理地图格子 - 使用SIMD指令
     * @param MapCells 地图格子数组
     * @param SimplificationFactor 简化因子
     */
    void ProcessMapCellsBatch(TArray<FMapCell>& MapCells, float SimplificationFactor) noexcept;

    /**
     * 【SIMD向量化升级】内存对齐的数据拷贝 - 使用SIMD优化
     * @param Source 源数据
     * @param Destination 目标数据
     */
    void CopyMapCellsAligned(const TArray<FMapCell>& Source, TArray<FMapCell>& Destination) noexcept;

    /**
     * 【SIMD向量化升级】获取SIMD操作统计
     * @return SIMD操作次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    int64 GetSIMDOperationsCount() const noexcept;

    /**
     * 【SIMD向量化升级】获取标量操作统计
     * @return 标量操作次数
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    int64 GetScalarOperationsCount() const noexcept;

    /**
     * 【SIMD向量化升级】获取SIMD效率比例
     * @return SIMD操作占总操作的比例 (0.0-1.0)
     */
    UFUNCTION(BlueprintCallable, Category = "Performance Optimizer|SIMD")
    float GetSIMDEfficiencyRatio() const noexcept;

    // 【已删除】分层内存池函数 - 已被统一对象池替代，避免重复实现
};
