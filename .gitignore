# 忽略Unreal Engine生成的文件和目录
/Binaries/
/Build/
/DerivedDataCache/
/Intermediate/
/Saved/
/Shaders/

# 忽略插件中的Intermediate文件夹
**/Intermediate/
**/Binaries/

# 忽略VS和JetBrains IDE文件
/.vs/
/.idea/
*.sln
*.suo
*.opensdf
*.sdf
*.VC.db
*.VC.opendb
*.DotSettings.user

# 忽略Python虚拟环境
/.venv/
/.zen_venv/

# 忽略日志和临时文件
*.log
*.bak
*.tmp

# 忽略编译报告
CompilationReport.md

# 忽略配置文件夹，但保留特定配置文件
/Config/*
!/Config/DefaultEngine.ini
!/Config/DefaultGame.ini
!/Config/DefaultInput.ini

# 忽略其他不需要版本控制的文件夹
/.vscode/
/data/

# 保留Source、Plugins和Content文件夹
# 这些文件夹下的内容将被Git管理
