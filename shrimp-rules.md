# UE5.6游戏项目开发守则

## 项目概述

### 核心目标
- 基于UE5.6最新功能实现大世界地图生成和性能优化系统
- 严格遵循ECS架构（Mass Entity系统）和数据驱动设计
- 集成ComputeFramework、FStreamableManager等UE5.6原生组件
- 使用现代化C++17/20特性和单一职责原则

### 技术栈要求
- **引擎版本**: Unreal Engine 5.6
- **C++标准**: C++17/20 (UE5.6支持的最新标准)
- **核心插件**: MassGameplay, ComputeFramework, ModelingToolsEditorMode
- **核心模块**: Engine, CoreUObject, AssetRegistry, MassEntity, MassSpawner

## UE5.6功能使用强制规范

### 智能指针使用规范

#### ✅ 必须使用
```cpp
// UE对象指针 - UE5.6推荐
TObjectPtr<UObject> MyObject;
TWeakObjectPtr<AActor> WeakActor;

// 共享指针
TSharedPtr<FMyClass> SharedData;
TUniquePtr<FMyClass> UniqueData;
```

#### ❌ 严格禁止
```cpp
// 原始指针 - 违反项目规范
UObject* RawPointer;  // 禁止
AActor* ActorPtr;     // 禁止

// 手动内存管理 - 违反UE5.6最佳实践
FMyClass* ManualPtr = new FMyClass();  // 禁止
```

## 代码重复检查和清理规范

### 🔍 实现前强制检查流程
1. **UE5.6 API搜索**：使用`resolve-library-id`和`get-library-docs`工具检查UE引擎是否已有相同功能
2. **项目代码搜索**：使用`codebase-retrieval`工具搜索项目中是否存在类似实现
3. **Mass Entity检查**：优先检查Mass Components和Processors是否已有相关功能
4. **Performance系统检查**：检查`Source/Game/Public/Performance`目录下是否已有相关功能

### 🧹 代码清理强制要求
```cpp
// ❌ 必须删除的废弃代码示例
// 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

// ❌ 必须删除的重复实现
class FUnifiedObjectPool { /* 重复实现，必须删除 */ };
class FTieredObjectPool { /* 保留更优实现 */ };

// ✅ 保留的优化实现
template<typename T>
class FTieredObjectPool {
    // 三层存储：Hot/Warm/Cold
};
```

### 📝 增量更新规范
```cpp
// ✅ 正确的增量更新方式
void ExistingFunction(int32 NewParam = 0) // 添加默认参数保持兼容性
{
    // 保留原有逻辑
    OriginalLogic();

    // 添加新功能
    if (NewParam > 0) {
        NewFeatureLogic(NewParam);
    }
}

// ❌ 禁止完全重写
void ExistingFunction() // 不要删除原有实现重新写
{
    // 完全新的实现 - 禁止这样做
}
```

### UE引擎功能检查规范

#### 🔍 代码实现前必须检查
- **搜索UE5.6 API文档**：确认引擎是否已有相同功能
- **检查Mass Entity系统**：优先使用Mass Components和Processors
- **检查ComputeFramework**：GPU计算任务必须使用ComputeFramework
- **检查现有Subsystem**：继承现有UWorldSubsystem架构

## ECS架构和数据驱动设计强制规范

### Mass Entity系统强制使用

#### ✅ 必须使用的ECS模式
```cpp
// Mass Component - 数据存储
USTRUCT()
struct GAME_API FPerformanceComponent : public FMassFragment
{
    GENERATED_BODY()

    UPROPERTY()
    float OptimizationLevel = 1.0f;

    UPROPERTY()
    int32 CacheHitCount = 0;
};

// Mass Processor - 逻辑处理
UCLASS()
class GAME_API UPerformanceProcessor : public UMassProcessor
{
    GENERATED_BODY()

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override;
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override;

private:
    FMassEntityQuery EntityQuery;
};
```

#### ❌ 禁止的传统OOP模式
```cpp
// 禁止创建传统的游戏对象类
class APerformanceActor : public AActor // 违反ECS架构
{
    // 不要在Actor中混合数据和逻辑
};
```

### 数据驱动设计强制要求

#### ✅ 必须使用UDataAsset配置
```cpp
// 配置数据资产
UCLASS(BlueprintType)
class GAME_API UPerformanceConfigDataAsset : public UDataAsset
{
    GENERATED_BODY()

public:
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Object Pool")
    int32 MaxPoolSize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Cache")
    float CacheExpirationTime = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GPU Compute")
    bool bEnableGPUCompute = true;
};
```

#### ❌ 禁止硬编码配置
```cpp
// 禁止在代码中硬编码配置值
const int32 POOL_SIZE = 1000; // 违反数据驱动原则
const float CACHE_TIME = 300.0f; // 必须移到DataAsset中
```

## Performance系统强制使用规范

### 🎯 Performance文件夹强制使用要求

#### ✅ 必须优先使用Performance系统组件
在需要性能优化时，必须首先检查并使用`Source/Game/Public/Performance`和`Source/Game/Private/Performance`文件夹中的现有组件：

**核心Performance组件：**
- **PerformanceOptimizer.h/.cpp** - 主性能优化器，协调所有性能子系统
- **ObjectPoolManager.h/.cpp** - 对象池管理器，提供分层对象池功能
- **CacheManager.h/.cpp** - 缓存管理器，提供多级缓存系统
- **GPUComputeManager.h/.cpp** - GPU计算管理器，管理ComputeFramework
- **PerformanceComponent.h/.cpp** - Mass Entity性能组件
- **PerformanceProcessor.h/.cpp** - Mass Entity性能处理器

#### ✅ 对象池系统强制使用规范
```cpp
// 【必须】通过ObjectPoolManager获取对象池实例
UObjectPoolManager* PoolManager = GetWorld()->GetSubsystem<UObjectPoolManager>();

// 【推荐】使用分层对象池获取对象
TSharedPtr<FMapCell> Cell = PoolManager->GetTieredPooledObject<FMapCell>(ETier::Hot);
TSharedPtr<AActor> Actor = PoolManager->GetTieredPooledObject<AActor>(ETier::Warm);

// 【必须】归还对象到指定层级
PoolManager->ReturnTieredPooledObject<FMapCell>(Cell, ETier::Hot);
PoolManager->ReturnTieredPooledObject<AActor>(Actor, ETier::Warm);

// 【高级】批量获取对象（大量对象场景）
TArray<TSharedPtr<FMapCell>> Cells;
PoolManager->GetBatchPooledObjects<FMapCell>(Cells, 100, ETier::Hot);
```

#### ✅ 缓存系统强制使用规范
```cpp
// 【必须】通过CacheManager访问缓存系统
UCacheManager* CacheManager = GetWorld()->GetSubsystem<UCacheManager>();

// 【推荐】使用多级缓存存储数据
FString CachedData = CacheManager->GetCachedData(Key, ECacheLevel::L1);
CacheManager->SetCachedData(Key, Data, ECacheLevel::L1, ExpirationTime);

// 【高级】使用压缩缓存（大数据场景）
TArray<uint8> CompressedData = CacheManager->GetCompressedCachedData(Key, ECompressionAlgorithm::LZ4);
CacheManager->SetCompressedCachedData(Key, Data, ECompressionAlgorithm::LZ4, ECacheLevel::L2);

// 【性能监控】获取缓存统计信息
FCacheStats Stats = CacheManager->GetCacheStatistics();
UE_LOG(LogTemp, Log, TEXT("【缓存统计】命中率: %.2f%%, 内存使用: %d MB"),
       Stats.HitRate * 100.0f, Stats.MemoryUsageMB);
```

#### ✅ GPU计算系统强制使用规范
```cpp
// 【必须】通过GPUComputeManager访问GPU计算功能
UGPUComputeManager* GPUManager = GetWorld()->GetSubsystem<UGPUComputeManager>();

// 【推荐】检查GPU计算可用性
if (GPUManager->IsGPUComputeAvailable())
{
    // 【UE5.6】使用ComputeFramework执行GPU计算
    UComputeGraphComponent* ComputeComponent = GPUManager->GetComputeGraphComponent();
    if (IsValid(ComputeComponent))
    {
        ComputeComponent->QueueExecute();
    }
}

// 【回退机制】GPU不可用时的CPU回退
if (!GPUManager->IsGPUComputeAvailable())
{
    // 使用CPU实现相同功能
    GPUManager->ExecuteCPUFallback(ComputeData);
}
```

#### ❌ 严格禁止的Performance违规行为
```cpp
// 【禁止】创建自定义对象池
class MyCustomPool { /* 违反项目规范 - 必须使用ObjectPoolManager */ };

// 【禁止】直接内存分配大量对象
FMapCell* Cell = new FMapCell(); // 违反 - 必须使用对象池
delete Cell; // 违反 - 必须使用对象池
TArray<FMapCell> DirectArray; // 违反 - 大量对象必须使用对象池

// 【禁止】绕过Performance系统的缓存
TMap<FString, FString> CustomCache; // 违反 - 必须使用CacheManager

// 【禁止】直接使用RHI进行GPU计算
FRHICommandListImmediate& RHICmdList = FRHICommandListExecutor::GetImmediateCommandList(); // 违反 - 必须使用GPUComputeManager

// 【禁止】忽略性能监控
void SomeFunction()
{
    // 错误：没有使用PerformanceComponent进行性能监控
    // 正确：应该通过PerformanceOptimizer记录性能数据
}
```

### 🖥️ GPU计算强制规范

#### ✅ 必须使用ComputeFramework
```cpp
// 获取GPU计算组件
UComputeGraphComponent* ComputeComponent = Optimizer->GetGPUComputeComponent();

// 执行GPU计算
if (ComputeComponent && Optimizer->IsGPUComputeEnabled()) {
    ComputeComponent->QueueExecute();
}
```

#### ❌ 禁止自定义GPU计算
```cpp
// 禁止直接使用RHI进行GPU计算
FRHICommandListImmediate& RHICmdList = FRHICommandListExecutor::GetImmediateCommandList(); // 禁止
```

## 单一职责原则强制规范

### 🎯 类职责划分强制要求

#### ✅ 正确的单一职责设计
```cpp
// 每个Manager只负责一个领域
UCLASS()
class GAME_API UObjectPoolManager : public UWorldSubsystem
{
    // 只负责对象池管理
};

UCLASS()
class GAME_API UCacheManager : public UWorldSubsystem
{
    // 只负责缓存管理
};

UCLASS()
class GAME_API UGPUComputeManager : public UWorldSubsystem
{
    // 只负责GPU计算管理
};
```

#### ❌ 违反单一职责的设计
```cpp
// 禁止一个类承担多个职责
UCLASS()
class UEverythingManager : public UWorldSubsystem
{
    // 对象池 + 缓存 + GPU计算 + 网络 + AI - 违反单一职责原则
};
```

### 🔧 函数职责划分

#### ✅ 函数单一职责
```cpp
// 每个函数只做一件事
void InitializeObjectPool();
void CleanupExpiredCache();
void ExecuteGPUComputation();

// 复合操作通过组合实现
void InitializePerformanceSystem()
{
    InitializeObjectPool();
    InitializeCacheSystem();
    InitializeGPUCompute();
}
```

#### ❌ 违反单一职责的函数
```cpp
// 禁止一个函数做多件不相关的事
void DoEverything() // 违反单一职责
{
    InitializePool();
    LoadAssets();
    ProcessAI();
    RenderGraphics();
}
```

## UE5.6最新功能强制使用规范

### 🚀 必须优先使用的UE5.6功能

#### ✅ Instanced Actors（大量对象渲染优化）
```cpp
// 使用Instanced Actors替代传统Actor
UCLASS()
class GAME_API UInstancedActorManager : public UWorldSubsystem
{
public:
    // 创建实例化Actor
    void CreateInstancedActors(TSubclassOf<AActor> ActorClass, const TArray<FTransform>& Transforms);
};
```

#### ✅ 最新异步API
```cpp
// 使用AsyncTask替代过时的Async
AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []() {
    // 后台任务
});

// 使用FStreamableManager进行异步资源加载
StreamableManager->RequestAsyncLoad(AssetPaths, FStreamableDelegate::CreateLambda([this]() {
    // 加载完成回调
}));
```

#### ✅ ComputeFramework深度集成
```cpp
// 使用ComputeFramework进行GPU计算
UCLASS()
class GAME_API UMyComputeGraph : public UComputeGraph
{
    // GPU计算图定义
};
```

#### ❌ 禁止使用过时功能
```cpp
// 过时的异步API
Async(EAsyncExecution::ThreadPool, []() {}); // 禁止使用

// 过时的资源加载方式
UObject* Asset = LoadObject<UObject>(nullptr, TEXT("/Path/To/Asset")); // 禁止同步加载
```

// 3. 項目特定頭文件
#include "LevelGen/MapUtil.h"

// 4. 現代化C++標準庫 (謹慎使用)
#include <memory>
#include <optional>

// 5. 生成的頭文件 (必須最後)
#include "ClassName.generated.h"
```

### 多文件連動修改規則

#### 修改PerformanceOptimizer.h時必須同步修改
- `Source/Game/Private/Performance/PerformanceOptimizer.cpp`
- 所有相關的實現文件 (AsyncChunkLoader.cpp, AdvancedCompression.cpp等)

#### 修改枚舉定義時必須同步檢查
- 所有使用該枚舉的.cpp文件
- 所有相關的switch語句
- 所有相關的日誌輸出

#### 添加新的成員變量時必須
- 在構造函數中初始化
- 在Initialize()函數中設置
- 在Shutdown()函數中清理

## 性能優化專用規範

### 日誌標籤規範

#### ✅ 必須使用中文標籤
```cpp
UE_LOG(LogTemp, Log, TEXT("【UE5.6優化】功能初始化完成"));
UE_LOG(LogTemp, VeryVerbose, TEXT("【異步加載】塊加載完成: (%d, %d)"), X, Y);
UE_LOG(LogTemp, Warning, TEXT("【內存壓力】內存使用率過高: %.1f%%"), Pressure);
```

### 性能統計規範

#### ✅ 必須使用UE5.6性能工具
```cpp
// 使用FPlatformMemory獲取內存統計
const FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();

// 使用FDateTime替代FPlatformTime::Seconds()
const FDateTime CurrentTime = FDateTime::UtcNow();

// 使用原子操作進行線程安全統計
std::atomic<int32> Counter{0};
Counter.fetch_add(1, std::memory_order_relaxed);
```

### 緩存系統規範

#### ✅ 必須使用UE5.6緩存最佳實踐
```cpp
// 使用FStreamableManager進行資源緩存
StreamableManager->RequestAsyncLoad(AssetPaths, Delegate);

// 使用IAssetRegistry進行資源查詢
AssetRegistry->GetAssetsByClass(UStaticMesh::StaticClass(), AssetData);
```

## 錯誤處理規範

### ✅ 必須使用UE5.6錯誤處理機制
```cpp
// 使用check宏進行斷言
check(IsValid(MyObject));
checkf(Condition, TEXT("錯誤信息: %s"), *ErrorString);

// 使用ensure進行非致命檢查
if (!ensure(IsValid(MyObject)))
{
    return false;
}

// 使用UE_LOG進行錯誤記錄
UE_LOG(LogTemp, Error, TEXT("【錯誤】操作失敗: %s"), *ErrorMessage);
```

### ❌ 禁止使用標準C++異常
```cpp
// 禁止使用try/catch
try {
    // 代碼
} catch (...) {
    // 處理
}

// 禁止拋出異常
throw std::runtime_error("錯誤");
```

## 禁止事項

### 絕對禁止的編程實踐

#### ❌ 內存管理禁止事項
- 使用原始指針管理UE對象
- 手動調用new/delete
- 忽略內存泄漏檢查
- 在析構函數中訪問UE對象

#### ❌ 異步編程禁止事項
- 在非主線程直接操作UE對象
- 忽略線程安全問題
- 使用過時的異步API
- 阻塞主線程進行長時間操作

#### ❌ 性能優化禁止事項
- 在Tick函數中進行重型計算
- 忽略LOD系統
- 過度使用反射
- 忽略內存對齊

#### ❌ 代码结构禁止事项
- 在头文件中包含大型实现
- 循环依赖
- 违反单一职责原则
- 重复实现UE5.6已有功能

## 项目逻辑一致性和设计意图保证

### 🎯 设计意图强制检查清单

#### ✅ 每次代码修改必须验证
1. **ECS架构一致性**：所有游戏逻辑必须通过Mass Entity系统实现
2. **数据驱动设计**：所有配置必须通过UDataAsset管理
3. **单一职责原则**：每个类/函数只负责一个明确的职责
4. **性能优化目标**：所有修改必须考虑性能影响
5. **UE5.6功能优先**：优先使用引擎最新功能而非自定义实现

#### ✅ 逻辑一致性验证
```cpp
// 正确的逻辑流程
void ProcessGameLogic()
{
    // 1. 通过Mass Entity系统获取数据
    FMassEntityQuery Query;
    Query.ForEachEntityChunk(EntityManager, Context, [](FMassExecutionContext& Context) {
        // 2. 批量处理实体数据
        ProcessEntitiesInBatch(Context);
    });

    // 3. 使用Performance系统优化
    UPerformanceOptimizer* Optimizer = GetPerformanceOptimizer();
    Optimizer->OptimizeCurrentFrame();
}
```

#### ❌ 违反设计意图的实现
```cpp
// 违反ECS架构
void ProcessGameLogic()
{
    // 直接操作Actor - 违反ECS设计意图
    for (AActor* Actor : AllActors) {
        Actor->ProcessLogic(); // 错误：应该使用Mass Entity
    }
}
```

### 🔧 AI Agent决策优先级

#### 🎯 核心架构决策优先级
1. **UE5.6原生功能** > 自定义实现
2. **Mass Entity系统** > 传统Actor系统
3. **Performance系统对象池** > 直接内存分配
4. **数据驱动配置** > 硬编码值
5. **增量更新** > 完全重写
6. **单一职责设计** > 大而全的类

#### 🏗️ 架构模式选择优先级
7. **事件驱动通信** > 直接函数调用
8. **MVVM模式(UI开发)** > 传统UI架构
9. **命令模式(复杂操作)** > 直接执行逻辑
10. **MVC分层(游戏逻辑)** > 单体架构
11. **异步事件处理** > 同步阻塞处理
12. **批处理命令执行** > 单个命令执行

#### 🔄 集成模式优先级
13. **多模式协同** > 单一模式使用
14. **事件驱动+命令模式** > 传统回调机制
15. **MVVM+Mass Entity** > 传统UI数据绑定
16. **命令模式+对象池** > 直接对象创建
17. **MVC+事件系统** > 紧耦合架构

### ⚠️ 关键文件同步修改规范
- **修改PerformanceOptimizer**时，必须同步更新相关Manager子系统
- **添加Mass Component**时，必须创建对应的Mass Processor
- **修改DataAsset结构**时，必须更新所有使用该配置的代码
- **添加新的Subsystem**时，必须在Game.uproject中声明依赖
- 全局變量 (除非必要)
- 硬編碼魔法數字

### 特定於本項目的禁止事項

#### ❌ 性能優化器特定禁止
- 修改核心優化邏輯時不更新性能統計
- 添加新的緩存級別時不更新清理邏輯
- 修改異步加載時不考慮優先級系統
- 忽略GPU/CPU計算的回退機制

#### ❌ 事件驱动模式禁止事项
- 在非主线程直接触发UI相关事件
- 使用原始函数指针替代UE5.6委托系统
- 在事件处理中执行长时间阻塞操作
- 忽略事件处理的内存管理和生命周期
- 创建循环事件依赖导致无限递归

#### ❌ 命令模式禁止事项
- 在命令对象中存储大量临时数据
- 绕过命令队列直接执行命令逻辑
- 在命令执行中进行同步IO操作
- 忽略命令的撤销/重做机制设计
- 创建命令之间的直接依赖关系

#### ❌ MVC/MVVM架构禁止事项
- View层直接访问Model层数据
- Model层直接引用UI组件
- ViewModel中实现复杂业务逻辑
- 创建ViewModel之间的直接依赖
- 在非UI线程更新ViewModel属性
- 使用字符串进行属性绑定

## AI決策規範

### 模糊情況處理優先級
1. **優先使用UE5.6原生功能** - 總是優先選擇UE內置解決方案
2. **架構模式適配優先** - 根據場景選择最适合的架构模式
3. **保持向後兼容** - 修改時確保不破壞現有功能
4. **性能優先** - 在功能和性能之間選擇性能
5. **線程安全優先** - 多線程環境下優先考慮安全性
6. **模式集成優先** - 优先考虑多种架构模式的协同使用

### 架構模式選擇判斷標準
#### 🎯 事件驱动模式使用判断
- **使用场景**: 组件间松耦合通信、异步数据更新、多对多通信
- **判断条件**: `需要解耦 && (异步通信 || 多个监听者)`
- **优先级**: 事件驱动 > 直接调用 > 回调函数

#### 🎯 命令模式使用判断
- **使用场景**: 复杂操作序列、撤销重做、批处理、延迟执行
- **判断条件**: `操作复杂度高 || 需要撤销 || 需要批处理`
- **优先级**: 命令模式 > 直接执行 > 函数调用

#### 🎯 MVVM模式使用判断
- **使用场景**: UI数据绑定、实时界面更新、蓝图C++混合开发
- **判断条件**: `UI开发 && (数据绑定 || 实时更新)`
- **优先级**: MVVM > 传统UI架构 > 直接UI操作

#### 🎯 MVC模式使用判断
- **使用场景**: 复杂业务逻辑、游戏状态管理、单元测试需求
- **判断条件**: `业务逻辑复杂 || 需要单元测试 || 多控制器协调`
- **优先级**: MVC分层 > 单体架构 > 混合架构

### 決策樹
```
需要添加新功能？
├─ 是否有UE5.6原生支持？
│  ├─ 是 → 使用UE原生功能
│  └─ 否 → 檢查是否可以擴展現有系統
├─ 功能类型判断？
│  ├─ UI相关 → 考虑MVVM模式
│  │  ├─ 需要数据绑定？ → 使用MVVM + UE5.6 ModelViewViewModel
│  │  └─ 简单UI → 传统UMG实现
│  ├─ 组件通信 → 考虑事件驱动模式
│  │  ├─ 需要解耦？ → 使用事件驱动 + UE5.6委托系统
│  │  └─ 简单调用 → 直接函数调用
│  ├─ 复杂操作 → 考虑命令模式
│  │  ├─ 需要撤销/批处理？ → 使用命令模式 + Mass Entity
│  │  └─ 简单操作 → 直接执行
│  └─ 业务逻辑 → 考虑MVC模式
│     ├─ 逻辑复杂？ → 使用MVC分层 + Mass Entity
│     └─ 简单逻辑 → 单体实现
├─ 是否影響性能？
│  ├─ 是 → 添加性能監控和優化 + Performance系统集成
│  └─ 否 → 正常實現
├─ 是否需要多线程？
│  ├─ 是 → 使用UE異步框架 + 事件驱动异步处理
│  └─ 否 → 在主線程實現
└─ 是否需要多模式集成？
   ├─ 是 → 设计模式协同方案 (事件驱动+命令模式+MVVM等)
   └─ 否 → 使用单一模式
```

## 關鍵文件交互規範

### 核心文件依賴關係

#### PerformanceOptimizer.h 修改時必須檢查
- `PerformanceOptimizer.cpp` - 主實現文件
- `AsyncChunkLoader.cpp` - 異步加載實現
- `AdvancedCompression.cpp` - 壓縮算法實現
- `MemoryMappedCache.cpp` - 內存映射實現

#### 枚舉修改連動規則
```cpp
// 修改 ELODLevel 時必須同步更新
- CalculateLODLevel() 函數中的所有分支
- ApplyLODToChunk() 函數中的處理邏輯
- SimplifyMapData() 函數中的LOD處理
- 所有相關的日誌輸出和統計
```

#### 結構體修改連動規則
```cpp
// 修改 FMapChunk 時必須同步更新
- 所有使用該結構體的序列化代碼
- 相關的壓縮/解壓縮邏輯
- 緩存系統的存儲格式
- 網絡同步相關代碼（如果存在）
```

### 模塊間接口規範

#### 異步加載模塊接口
- 必須通過 `FAsyncChunkLoader` 類進行所有異步操作
- 禁止直接調用底層異步API
- 必須使用優先級隊列系統

#### 緩存模塊接口
- 必須通過 `CacheMapData()` 和 `GetCachedMapData()` 進行緩存操作
- 禁止直接操作 `CacheEntries` 容器
- 必須考慮緩存級別和壓縮策略

#### 對象池模塊接口
- 必須通過 `GetPooledObject<T>()` 和 `ReturnPooledObject<T>()` 操作
- 禁止直接操作池容器
- 必須考慮分層池和統一池的回退機制

## 版本控制和更新規範

### 代碼更新時必須執行的檢查清單

#### ✅ 功能更新檢查清單
1. 更新相關的中文註釋標籤
2. 更新性能統計相關代碼
3. 檢查是否需要更新初始化/清理邏輯
4. 驗證線程安全性
5. 更新相關的錯誤處理
6. 檢查內存管理是否正確

#### ✅ 接口更新檢查清單
1. 檢查所有調用該接口的代碼
2. 更新相關的模板特化
3. 檢查藍圖接口的兼容性
4. 更新相關的序列化代碼
5. 檢查網絡同步影響（如果適用）

#### ✅ 性能優化更新檢查清單
1. 添加性能監控點
2. 更新相關統計信息
3. 檢查是否需要新的優化參數
4. 驗證多線程性能影響
5. 測試內存使用變化

### UE5.6特定更新要求

#### 引擎API更新適配
- 優先使用UE5.6新增的API替代舊版本
- 檢查廢棄API的替代方案
- 更新頭文件包含以使用最新模塊

#### 現代化C++特性採用
- 逐步將舊代碼重構為現代化C++
- 使用auto、constexpr等關鍵字提升代碼質量
- 採用RAII模式改善資源管理

## 測試和驗證規範

### 必須進行的測試類型

#### ✅ 性能測試要求
```cpp
// 必須測試的性能指標
- 內存使用量變化
- CPU使用率影響
- 加載時間變化
- 緩存命中率
- 對象池效率
```

#### ✅ 穩定性測試要求
```cpp
// 必須測試的穩定性場景
- 長時間運行測試
- 內存壓力測試
- 多線程併發測試
- 異常情況恢復測試
```

#### ✅ 兼容性測試要求
```cpp
// 必須測試的兼容性
- 不同LOD級別切換
- 不同壓縮算法切換
- 不同緩存策略切換
- 異步/同步模式切換
```

## 事件驱动模式强制规范

### 🎯 UE5.6事件系统强制使用

#### ✅ 必须使用的事件驱动模式
```cpp
// 基于UE5.6委托系统的事件定义
DECLARE_MULTICAST_DELEGATE_OneParam(FMassEntityEvent, FMassEntityHandle);
DECLARE_MULTICAST_DELEGATE_TwoParams(FPerformanceEvent, float, int32);
DECLARE_MULTICAST_DELEGATE_ThreeParams(FGameStateEvent, EGameState, EGameState, float);

// Mass Entity事件处理器
UCLASS()
class GAME_API UEventDrivenProcessor : public UMassProcessor
{
    GENERATED_BODY()

public:
    // 【UE5.6优化】事件处理器注册
    virtual void Initialize(UObject& Owner) override
    {
        Super::Initialize(Owner);
        RegisterEventHandlers();
        UE_LOG(LogTemp, Log, TEXT("【事件驱动】处理器初始化完成"));
    }

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override;
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override;

private:
    // 事件委托注册
    void RegisterEventHandlers()
    {
        if (UPerformanceOptimizer* Optimizer = GetWorld()->GetSubsystem<UPerformanceOptimizer>())
        {
            Optimizer->OnEntityCreated.AddUObject(this, &UEventDrivenProcessor::HandleEntityCreated);
            Optimizer->OnPerformanceChanged.AddUObject(this, &UEventDrivenProcessor::HandlePerformanceChanged);
        }
    }

    // 【异步事件】实体创建事件处理
    UFUNCTION()
    void HandleEntityCreated(FMassEntityHandle EntityHandle)
    {
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, EntityHandle]()
        {
            // 后台处理事件逻辑
            ProcessEntityCreationAsync(EntityHandle);
        });
    }

    // 【性能优化】性能变化事件处理
    UFUNCTION()
    void HandlePerformanceChanged(float NewLevel, int32 Priority)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【事件驱动】性能级别变更: %.2f, 优先级: %d"), NewLevel, Priority);

        // 使用对象池优化事件处理
        if (UPerformanceOptimizer* Optimizer = GetPerformanceOptimizer())
        {
            TSharedPtr<FEventData> EventData = Optimizer->GetPooledEventData();
            EventData->Level = NewLevel;
            EventData->Priority = Priority;
            ProcessPerformanceEvent(EventData);
            Optimizer->ReturnPooledEventData(EventData);
        }
    }

    FMassEntityQuery EntityQuery;
};
```

#### ✅ 事件命名和生命周期管理规范
```cpp
// 正确的事件命名规范
DECLARE_MULTICAST_DELEGATE_OneParam(FOnMapChunkLoaded, const FMapChunk&);     // 使用FOn前缀
DECLARE_MULTICAST_DELEGATE_OneParam(FOnEntityDestroyed, FMassEntityHandle);   // 明确事件含义
DECLARE_MULTICAST_DELEGATE_TwoParams(FOnPerformanceOptimized, float, bool);   // 参数类型明确

// 事件生命周期管理
UCLASS()
class GAME_API UEventManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【UE5.6最佳实践】事件注册管理
    template<typename DelegateType>
    void RegisterEventHandler(DelegateType& EventDelegate, UObject* Handler,
                             typename DelegateType::FDelegate::TMethodPtr<Handler> Method)
    {
        check(IsValid(Handler));
        EventDelegate.AddUObject(Handler, Method);

        // 【内存管理】记录事件绑定用于清理
        RegisteredHandlers.Add(Handler);
        UE_LOG(LogTemp, Log, TEXT("【事件管理】注册事件处理器: %s"), *Handler->GetName());
    }

    // 【线程安全】异步事件分发
    template<typename... Args>
    void BroadcastEventAsync(TMulticastDelegate<void(Args...)>& EventDelegate, Args... Arguments)
    {
        AsyncTask(ENamedThreads::GameThread, [EventDelegate, Arguments...]()
        {
            if (EventDelegate.IsBound())
            {
                EventDelegate.Broadcast(Arguments...);
            }
        });
    }

protected:
    virtual void Deinitialize() override
    {
        // 【内存安全】清理所有事件绑定
        for (TWeakObjectPtr<UObject> Handler : RegisteredHandlers)
        {
            if (Handler.IsValid())
            {
                // 清理该处理器的所有事件绑定
                CleanupHandlerBindings(Handler.Get());
            }
        }
        RegisteredHandlers.Empty();
        Super::Deinitialize();
    }

private:
    UPROPERTY()
    TArray<TWeakObjectPtr<UObject>> RegisteredHandlers;
};
```

### 🚀 与Mass Entity系统集成

#### ✅ Mass Entity事件集成模式
```cpp
// Mass Fragment事件数据
USTRUCT()
struct GAME_API FEventDrivenFragment : public FMassFragment
{
    GENERATED_BODY()

    // 【数据驱动】事件状态数据
    UPROPERTY()
    TArray<FGameplayTag> PendingEvents;

    UPROPERTY()
    float LastEventTime = 0.0f;

    UPROPERTY()
    int32 EventPriority = 0;
};

// Mass Processor事件处理
UCLASS()
class GAME_API UMassEventProcessor : public UMassProcessor
{
    GENERATED_BODY()

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override
    {
        EntityQuery.AddRequirement<FEventDrivenFragment>(EMassFragmentAccess::ReadWrite);
        EntityQuery.AddRequirement<FPerformanceComponent>(EMassFragmentAccess::ReadOnly);
    }

    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        // 【批处理优化】批量处理实体事件
        EntityQuery.ForEachEntityChunk(EntityManager, Context,
            [this](FMassExecutionContext& Context)
            {
                const TArrayView<FEventDrivenFragment> EventFragments =
                    Context.GetMutableFragmentView<FEventDrivenFragment>();
                const TConstArrayView<FPerformanceComponent> PerformanceComponents =
                    Context.GetFragmentView<FPerformanceComponent>();

                for (int32 EntityIndex = 0; EntityIndex < Context.GetNumEntities(); ++EntityIndex)
                {
                    ProcessEntityEvents(EventFragments[EntityIndex], PerformanceComponents[EntityIndex]);
                }
            });
    }

private:
    void ProcessEntityEvents(FEventDrivenFragment& EventFragment,
                           const FPerformanceComponent& PerformanceComponent)
    {
        // 【性能优化】基于性能级别处理事件
        const float CurrentTime = GetWorld()->GetTimeSeconds();
        if (CurrentTime - EventFragment.LastEventTime < GetEventProcessingInterval(PerformanceComponent.OptimizationLevel))
        {
            return; // 跳过高频事件处理
        }

        // 处理待处理事件
        for (const FGameplayTag& EventTag : EventFragment.PendingEvents)
        {
            BroadcastMassEntityEvent(EventTag, Context.GetEntity(EntityIndex));
        }

        EventFragment.PendingEvents.Empty();
        EventFragment.LastEventTime = CurrentTime;
    }

    FMassEntityQuery EntityQuery;
};
```

### ⚡ 异步事件处理优化

#### ✅ 必须使用的异步事件模式
```cpp
// 异步事件处理基类
UCLASS()
class GAME_API UAsyncEventHandler : public UObject
{
    GENERATED_BODY()

public:
    // 【UE5.6异步】使用现代异步API
    template<typename EventType>
    void ProcessEventAsync(const EventType& Event, TFunction<void(bool)> CompletionCallback)
    {
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask,
            [this, Event, CompletionCallback]()
            {
                bool bSuccess = ProcessEventInternal(Event);

                // 【线程安全】回到主线程执行回调
                AsyncTask(ENamedThreads::GameThread, [CompletionCallback, bSuccess]()
                {
                    CompletionCallback(bSuccess);
                });
            });
    }

    // 【性能监控】事件处理性能统计
    void TrackEventPerformance(const FString& EventName, float ProcessingTime)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【事件性能】%s 处理耗时: %.3fms"), *EventName, ProcessingTime * 1000.0f);

        // 更新性能统计
        if (UPerformanceOptimizer* Optimizer = GetPerformanceOptimizer())
        {
            Optimizer->UpdateEventProcessingStats(EventName, ProcessingTime);
        }
    }

protected:
    virtual bool ProcessEventInternal(const auto& Event) { return true; }

private:
    std::atomic<int32> ProcessingEventCount{0};
};
```

### ❌ 严格禁止的事件处理模式

#### ❌ 禁止的传统事件处理
```cpp
// 禁止直接使用原始函数指针
typedef void (*EventCallback)(int32); // 违反UE5.6最佳实践

// 禁止在非主线程直接操作UE对象
void BadEventHandler()
{
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []()
    {
        // 错误：在后台线程直接操作UObject
        UObject* SomeObject = NewObject<UObject>(); // 违反线程安全
    });
}

// 禁止忽略事件处理的内存管理
class BadEventManager
{
    TArray<UObject*> EventHandlers; // 错误：使用原始指针，可能导致悬空指针
};

// 禁止阻塞主线程的同步事件处理
void BadSyncEventHandler()
{
    // 错误：在主线程执行耗时操作
    FPlatformProcess::Sleep(1.0f); // 违反性能要求
}
```

#### ❌ 禁止的事件命名和设计
```cpp
// 禁止模糊的事件命名
DECLARE_MULTICAST_DELEGATE(FEvent);           // 错误：命名不明确
DECLARE_MULTICAST_DELEGATE(FOnSomething);     // 错误：过于模糊
DECLARE_MULTICAST_DELEGATE(FMyDelegate);      // 错误：非事件性命名

// 禁止过度复杂的事件参数
DECLARE_MULTICAST_DELEGATE_FiveParams(FComplexEvent, int32, float, bool, FString, TArray<UObject*>); // 错误：参数过多

// 禁止在事件处理中直接修改发送者状态
void BadEventHandler(UObject* Sender)
{
    // 错误：在事件处理中直接修改发送者，可能导致循环依赖
    Sender->SomeProperty = NewValue;
}
```

## 命令模式强制规范

### 🎯 UE5.6命令系统强制使用

#### ✅ 必须使用的命令模式基础架构
```cpp
// 基于UE5.6的命令基类定义
UCLASS(BlueprintType, Abstract)
class GAME_API UMassCommand : public UObject
{
    GENERATED_BODY()

public:
    // 【UE5.6优化】命令执行接口
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) PURE_VIRTUAL(UMassCommand::Execute,);

    // 【撤销重做】命令撤销接口
    virtual void Undo(FMassEntityManager& EntityManager, FMassExecutionContext& Context) {}

    // 【性能优化】命令是否可撤销
    virtual bool CanUndo() const { return false; }

    // 【批处理优化】命令是否可批处理
    virtual bool CanBatch() const { return true; }

    // 【数据驱动】命令优先级
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Command")
    int32 Priority = 0;

    // 【内存管理】命令执行时间戳
    UPROPERTY(BlueprintReadOnly, Category = "Command")
    double ExecutionTime = 0.0;

protected:
    // 【日志标签】命令执行日志
    virtual FString GetCommandName() const { return GetClass()->GetName(); }

    // 【性能监控】命令执行性能统计
    void TrackCommandPerformance(double StartTime, double EndTime)
    {
        const double Duration = EndTime - StartTime;
        UE_LOG(LogTemp, VeryVerbose, TEXT("【命令执行】%s 耗时: %.3fms"),
               *GetCommandName(), Duration * 1000.0);

        if (UPerformanceOptimizer* Optimizer = GetPerformanceOptimizer())
        {
            Optimizer->UpdateCommandExecutionStats(GetCommandName(), Duration);
        }
    }
};

// Mass Entity专用命令基类
UCLASS(BlueprintType, Abstract)
class GAME_API UMassEntityCommand : public UMassCommand
{
    GENERATED_BODY()

public:
    // 【Mass Entity】目标实体句柄
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Mass Entity")
    FMassEntityHandle TargetEntity;

    // 【批处理优化】实体命令批处理执行
    virtual void ExecuteBatch(const TArray<FMassEntityHandle>& Entities,
                             FMassEntityManager& EntityManager,
                             FMassExecutionContext& Context);

protected:
    // 【ECS架构】验证实体有效性
    bool IsEntityValid(const FMassEntityHandle& Entity, FMassEntityManager& EntityManager) const
    {
        return EntityManager.IsEntityValid(Entity);
    }
};
```

#### ✅ 命令处理器和队列管理
```cpp
// Mass命令处理器
UCLASS()
class GAME_API UMassCommandProcessor : public UMassProcessor
{
    GENERATED_BODY()

public:
    virtual void Initialize(UObject& Owner) override
    {
        Super::Initialize(Owner);
        InitializeCommandQueue();
        UE_LOG(LogTemp, Log, TEXT("【命令处理器】初始化完成"));
    }

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override;
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override;

    // 【批处理优化】批量处理命令队列
    void ProcessCommandQueue(FMassEntityManager& EntityManager, FMassExecutionContext& Context)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(MassCommandProcessor_ProcessQueue);

        // 【性能优化】按优先级排序命令
        PendingCommands.Sort([](const TObjectPtr<UMassCommand>& A, const TObjectPtr<UMassCommand>& B)
        {
            return A->Priority > B->Priority;
        });

        // 【批处理执行】分批处理命令
        TArray<TObjectPtr<UMassCommand>> BatchCommands;
        for (auto& Command : PendingCommands)
        {
            if (IsValid(Command) && Command->CanBatch())
            {
                BatchCommands.Add(Command);
                if (BatchCommands.Num() >= MaxBatchSize)
                {
                    ExecuteCommandBatch(BatchCommands, EntityManager, Context);
                    BatchCommands.Empty();
                }
            }
            else if (IsValid(Command))
            {
                // 单独执行不可批处理的命令
                ExecuteSingleCommand(Command, EntityManager, Context);
            }
        }

        // 处理剩余的批处理命令
        if (BatchCommands.Num() > 0)
        {
            ExecuteCommandBatch(BatchCommands, EntityManager, Context);
        }

        PendingCommands.Empty();
    }

private:
    // 【对象池优化】命令队列
    UPROPERTY()
    TArray<TObjectPtr<UMassCommand>> PendingCommands;

    // 【撤销重做】已执行命令历史
    UPROPERTY()
    TArray<TObjectPtr<UMassCommand>> ExecutedCommands;

    // 【性能配置】最大批处理大小
    UPROPERTY(EditAnywhere, Category = "Performance")
    int32 MaxBatchSize = 100;

    // 【内存管理】最大历史命令数量
    UPROPERTY(EditAnywhere, Category = "Memory")
    int32 MaxHistorySize = 1000;

    void InitializeCommandQueue()
    {
        PendingCommands.Reserve(MaxBatchSize);
        ExecutedCommands.Reserve(MaxHistorySize);
    }

    void ExecuteSingleCommand(TObjectPtr<UMassCommand> Command,
                             FMassEntityManager& EntityManager,
                             FMassExecutionContext& Context)
    {
        const double StartTime = FPlatformTime::Seconds();
        Command->Execute(EntityManager, Context);
        const double EndTime = FPlatformTime::Seconds();

        Command->TrackCommandPerformance(StartTime, EndTime);
        Command->ExecutionTime = EndTime;

        // 【撤销重做】保存可撤销命令
        if (Command->CanUndo())
        {
            ExecutedCommands.Add(Command);
            if (ExecutedCommands.Num() > MaxHistorySize)
            {
                ExecutedCommands.RemoveAt(0);
            }
        }
    }

    void ExecuteCommandBatch(const TArray<TObjectPtr<UMassCommand>>& Commands,
                            FMassEntityManager& EntityManager,
                            FMassExecutionContext& Context)
    {
        for (auto& Command : Commands)
        {
            ExecuteSingleCommand(Command, EntityManager, Context);
        }
    }

    FMassEntityQuery EntityQuery;
};
```

### 🔧 与Enhanced Input系统集成

#### ✅ 输入命令绑定规范
```cpp
// 输入命令管理器
UCLASS()
class GAME_API UInputCommandManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    virtual void Initialize(FSubsystemCollectionBase& Collection) override
    {
        Super::Initialize(Collection);
        SetupInputCommands();
        UE_LOG(LogTemp, Log, TEXT("【输入命令】管理器初始化完成"));
    }

    // 【Enhanced Input】绑定输入动作到命令
    template<typename CommandType>
    void BindInputActionToCommand(const UInputAction* InputAction,
                                 TSubclassOf<CommandType> CommandClass,
                                 int32 Priority = 0)
    {
        static_assert(TIsDerivedFrom<CommandType, UMassCommand>::IsDerived,
                     "CommandType must derive from UMassCommand");

        FInputCommandBinding Binding;
        Binding.InputAction = InputAction;
        Binding.CommandClass = CommandClass;
        Binding.Priority = Priority;

        InputCommandBindings.Add(Binding);
        UE_LOG(LogTemp, Log, TEXT("【输入绑定】动作 %s 绑定到命令 %s"),
               *InputAction->GetName(), *CommandClass->GetName());
    }

    // 【命令队列】处理输入触发的命令
    void ProcessInputCommand(const UInputAction* InputAction, const FInputActionValue& Value)
    {
        for (const FInputCommandBinding& Binding : InputCommandBindings)
        {
            if (Binding.InputAction == InputAction)
            {
                // 【对象池优化】从池中获取命令对象
                if (UPerformanceOptimizer* Optimizer = GetWorld()->GetSubsystem<UPerformanceOptimizer>())
                {
                    TObjectPtr<UMassCommand> Command = Optimizer->GetPooledCommand(Binding.CommandClass);
                    if (IsValid(Command))
                    {
                        // 【异步执行】将命令加入队列
                        QueueCommand(Command);
                    }
                }
                break;
            }
        }
    }

protected:
    // 输入命令绑定结构
    USTRUCT()
    struct FInputCommandBinding
    {
        GENERATED_BODY()

        UPROPERTY()
        TObjectPtr<const UInputAction> InputAction = nullptr;

        UPROPERTY()
        TSubclassOf<UMassCommand> CommandClass = nullptr;

        UPROPERTY()
        int32 Priority = 0;
    };

private:
    UPROPERTY()
    TArray<FInputCommandBinding> InputCommandBindings;

    void SetupInputCommands()
    {
        // 【数据驱动】从配置文件加载输入命令绑定
        if (const UInputCommandDataAsset* InputConfig = GetInputCommandConfig())
        {
            for (const auto& ConfigBinding : InputConfig->CommandBindings)
            {
                InputCommandBindings.Add(ConfigBinding);
            }
        }
    }

    void QueueCommand(TObjectPtr<UMassCommand> Command)
    {
        if (UMassCommandProcessor* CommandProcessor = GetWorld()->GetSubsystem<UMassCommandProcessor>())
        {
            CommandProcessor->AddCommand(Command);
        }
    }
};
```

### 🔄 撤销/重做机制实现

#### ✅ 命令历史管理规范
```cpp
// 命令历史管理器
UCLASS()
class GAME_API UCommandHistoryManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【撤销重做】执行撤销操作
    bool UndoLastCommand()
    {
        if (UndoStack.Num() == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("【撤销重做】没有可撤销的命令"));
            return false;
        }

        TObjectPtr<UMassCommand> Command = UndoStack.Pop();
        if (IsValid(Command) && Command->CanUndo())
        {
            // 【Mass Entity】获取实体管理器和执行上下文
            if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
            {
                FMassEntityManager& EntityManager = MassSubsystem->GetMutableEntityManager();
                FMassExecutionContext Context(EntityManager);

                const double StartTime = FPlatformTime::Seconds();
                Command->Undo(EntityManager, Context);
                const double EndTime = FPlatformTime::Seconds();

                Command->TrackCommandPerformance(StartTime, EndTime);
                RedoStack.Push(Command);

                UE_LOG(LogTemp, Log, TEXT("【撤销重做】成功撤销命令: %s"), *Command->GetCommandName());
                return true;
            }
        }

        return false;
    }

    // 【撤销重做】执行重做操作
    bool RedoLastCommand()
    {
        if (RedoStack.Num() == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("【撤销重做】没有可重做的命令"));
            return false;
        }

        TObjectPtr<UMassCommand> Command = RedoStack.Pop();
        if (IsValid(Command))
        {
            if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
            {
                FMassEntityManager& EntityManager = MassSubsystem->GetMutableEntityManager();
                FMassExecutionContext Context(EntityManager);

                const double StartTime = FPlatformTime::Seconds();
                Command->Execute(EntityManager, Context);
                const double EndTime = FPlatformTime::Seconds();

                Command->TrackCommandPerformance(StartTime, EndTime);
                UndoStack.Push(Command);

                UE_LOG(LogTemp, Log, TEXT("【撤销重做】成功重做命令: %s"), *Command->GetCommandName());
                return true;
            }
        }

        return false;
    }

    // 【内存管理】添加命令到历史记录
    void AddCommandToHistory(TObjectPtr<UMassCommand> Command)
    {
        if (IsValid(Command) && Command->CanUndo())
        {
            UndoStack.Push(Command);

            // 【内存优化】限制历史记录大小
            if (UndoStack.Num() > MaxHistorySize)
            {
                UndoStack.RemoveAt(0);
            }

            // 清空重做栈
            RedoStack.Empty();
        }
    }

    // 【性能监控】获取历史统计信息
    void GetHistoryStats(int32& UndoCount, int32& RedoCount) const
    {
        UndoCount = UndoStack.Num();
        RedoCount = RedoStack.Num();
    }

private:
    // 【对象池优化】撤销命令栈
    UPROPERTY()
    TArray<TObjectPtr<UMassCommand>> UndoStack;

    // 【对象池优化】重做命令栈
    UPROPERTY()
    TArray<TObjectPtr<UMassCommand>> RedoStack;

    // 【内存配置】最大历史记录大小
    UPROPERTY(EditAnywhere, Category = "Memory", meta = (ClampMin = "10", ClampMax = "10000"))
    int32 MaxHistorySize = 1000;
};
```

### ⚡ 命令性能优化规范

#### ✅ 批处理和对象池优化
```cpp
// 批处理命令基类
UCLASS(Abstract)
class GAME_API UBatchableCommand : public UMassCommand
{
    GENERATED_BODY()

public:
    // 【批处理优化】批量执行接口
    virtual void ExecuteBatch(const TArray<TObjectPtr<UMassCommand>>& Commands,
                             FMassEntityManager& EntityManager,
                             FMassExecutionContext& Context)
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(BatchableCommand_ExecuteBatch);

        // 【性能优化】预分配内存
        TArray<FMassEntityHandle> BatchEntities;
        BatchEntities.Reserve(Commands.Num());

        // 【Mass Entity】收集所有目标实体
        for (const auto& Command : Commands)
        {
            if (const UMassEntityCommand* EntityCommand = Cast<UMassEntityCommand>(Command))
            {
                if (IsEntityValid(EntityCommand->TargetEntity, EntityManager))
                {
                    BatchEntities.Add(EntityCommand->TargetEntity);
                }
            }
        }

        // 【批处理执行】一次性处理所有实体
        if (BatchEntities.Num() > 0)
        {
            ExecuteEntityBatch(BatchEntities, EntityManager, Context);
        }
    }

protected:
    // 【纯虚函数】子类实现具体的批处理逻辑
    virtual void ExecuteEntityBatch(const TArray<FMassEntityHandle>& Entities,
                                   FMassEntityManager& EntityManager,
                                   FMassExecutionContext& Context) PURE_VIRTUAL(UBatchableCommand::ExecuteEntityBatch,);
};

// 命令对象池管理
UCLASS()
class GAME_API UCommandPoolManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【对象池优化】获取池化命令对象
    template<typename CommandType>
    TObjectPtr<CommandType> GetPooledCommand()
    {
        static_assert(TIsDerivedFrom<CommandType, UMassCommand>::IsDerived,
                     "CommandType must derive from UMassCommand");

        const UClass* CommandClass = CommandType::StaticClass();

        // 【内存优化】从对象池获取
        if (TArray<TObjectPtr<UMassCommand>>* Pool = CommandPools.Find(CommandClass))
        {
            if (Pool->Num() > 0)
            {
                TObjectPtr<UMassCommand> Command = Pool->Pop();
                if (IsValid(Command))
                {
                    // 【对象重置】重置命令状态
                    ResetCommand(Command);
                    return Cast<CommandType>(Command);
                }
            }
        }

        // 【对象创建】池中无可用对象时创建新对象
        TObjectPtr<CommandType> NewCommand = NewObject<CommandType>(this);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【对象池】创建新命令对象: %s"), *CommandClass->GetName());
        return NewCommand;
    }

    // 【对象池优化】归还命令对象到池
    void ReturnCommandToPool(TObjectPtr<UMassCommand> Command)
    {
        if (!IsValid(Command))
        {
            return;
        }

        const UClass* CommandClass = Command->GetClass();
        TArray<TObjectPtr<UMassCommand>>& Pool = CommandPools.FindOrAdd(CommandClass);

        // 【内存限制】限制池大小
        if (Pool.Num() < MaxPoolSize)
        {
            Pool.Add(Command);
            UE_LOG(LogTemp, VeryVerbose, TEXT("【对象池】归还命令对象: %s"), *CommandClass->GetName());
        }
        else
        {
            // 池已满，让对象自然销毁
            UE_LOG(LogTemp, VeryVerbose, TEXT("【对象池】池已满，销毁命令对象: %s"), *CommandClass->GetName());
        }
    }

private:
    // 【对象池存储】按类型分组的命令池
    UPROPERTY()
    TMap<TObjectPtr<const UClass>, TArray<TObjectPtr<UMassCommand>>> CommandPools;

    // 【内存配置】单个池的最大大小
    UPROPERTY(EditAnywhere, Category = "Performance")
    int32 MaxPoolSize = 100;

    void ResetCommand(TObjectPtr<UMassCommand> Command)
    {
        // 重置命令的基本属性
        Command->Priority = 0;
        Command->ExecutionTime = 0.0;

        // 如果是实体命令，重置实体句柄
        if (UMassEntityCommand* EntityCommand = Cast<UMassEntityCommand>(Command))
        {
            EntityCommand->TargetEntity.Reset();
        }
    }
};
```

### ❌ 严格禁止的命令模式实现

#### ❌ 禁止的命令设计模式
```cpp
// 禁止直接在命令中存储大量数据
class BadCommand : public UMassCommand
{
    // 错误：在命令中存储大量数据，影响内存效率
    UPROPERTY()
    TArray<FVector> LargeDataArray; // 违反轻量级命令原则

    UPROPERTY()
    UTexture2D* HeavyTexture; // 错误：存储重型资源引用
};

// 禁止在命令执行中进行同步IO操作
class BadIOCommand : public UMassCommand
{
public:
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        // 错误：在命令执行中进行同步文件操作
        FString FileContent;
        FFileHelper::LoadFileToString(FileContent, TEXT("SomeFile.txt")); // 违反性能要求

        // 错误：在命令执行中进行网络请求
        // HttpRequest->ProcessRequest(); // 阻塞主线程
    }
};

// 禁止命令之间的直接依赖
class BadDependentCommand : public UMassCommand
{
    // 错误：命令直接依赖其他命令实例
    UPROPERTY()
    TObjectPtr<UMassCommand> DependentCommand; // 违反命令独立性原则

public:
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        // 错误：在命令内部直接执行其他命令
        if (IsValid(DependentCommand))
        {
            DependentCommand->Execute(EntityManager, Context); // 违反命令队列管理
        }
    }
};

// 禁止在命令中直接操作UI
class BadUICommand : public UMassCommand
{
public:
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        // 错误：在命令中直接操作UI组件
        if (UUserWidget* Widget = CreateWidget<UUserWidget>(GetWorld()))
        {
            Widget->AddToViewport(); // 违反职责分离原则
        }
    }
};
```

#### ❌ 禁止的命令队列管理
```cpp
// 禁止不安全的多线程命令处理
class BadThreadCommand
{
    void ProcessCommandsUnsafely()
    {
        // 错误：在多线程环境中不安全地访问命令队列
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
        {
            // 错误：后台线程直接操作UE对象
            for (auto& Command : Commands)
            {
                Command->Execute(EntityManager, Context); // 违反线程安全
            }
        });
    }

    TArray<TObjectPtr<UMassCommand>> Commands; // 错误：无线程保护的容器
};

// 禁止无限制的命令历史记录
class BadHistoryManager
{
    // 错误：无限制地保存命令历史，导致内存泄漏
    TArray<TObjectPtr<UMassCommand>> UnlimitedHistory; // 违反内存管理原则

    void AddCommand(TObjectPtr<UMassCommand> Command)
    {
        // 错误：不检查内存限制直接添加
        UnlimitedHistory.Add(Command); // 可能导致内存溢出
    }
};
```

## MVC/MVVM架构模式强制规范

### 🎯 架构模式适用场景判断

#### ✅ MVVM模式适用场景（UI开发优先）
```cpp
// 【UI开发】MVVM模式强制使用场景
// 1. 复杂UI界面的数据绑定
// 2. 实时数据更新的界面显示
// 3. 多个UI组件共享相同数据源
// 4. 需要数据验证和格式化的表单
// 5. 支持蓝图和C++双向开发的界面

// 正确的MVVM使用判断
bool ShouldUseMVVMPattern(const FUIRequirement& Requirement)
{
    return Requirement.HasDataBinding() ||
           Requirement.HasRealTimeUpdates() ||
           Requirement.HasMultipleViews() ||
           Requirement.RequiresBlueprintSupport();
}
```

#### ✅ MVC模式适用场景（游戏逻辑分层）
```cpp
// 【游戏逻辑】MVC模式强制使用场景
// 1. 复杂的游戏状态管理
// 2. 多个系统协调的业务逻辑
// 3. 需要单元测试的核心逻辑
// 4. 跨平台共享的游戏规则
// 5. 与Mass Entity系统集成的大规模逻辑

// 正确的MVC使用判断
bool ShouldUseMVCPattern(const FGameLogicRequirement& Requirement)
{
    return Requirement.HasComplexBusinessLogic() ||
           Requirement.RequiresUnitTesting() ||
           Requirement.HasMultipleControllers() ||
           Requirement.IntegratesWithMassEntity();
}
```

### 🖥️ UE5.6 MVVM模块强制使用规范

#### ✅ ViewModel基类标准实现
```cpp
// 基于UE5.6 ModelViewViewModel模块的ViewModel基类
UCLASS(BlueprintType, Blueprintable)
class GAME_API UGameDataViewModel : public UMVVMViewModelBase
{
    GENERATED_BODY()

public:
    // 【数据绑定】使用FieldNotify进行属性通知
    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Player Data")
    int32 PlayerScore = 0;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Player Data")
    float PlayerHealth = 100.0f;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Player Data")
    FString PlayerName = TEXT("Player");

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Game State")
    bool bIsGamePaused = false;

    // 【UE5.6最佳实践】使用UE_MVVM_SET_PROPERTY_VALUE宏更新属性
    UFUNCTION(BlueprintCallable, Category = "Player Data")
    void UpdatePlayerScore(int32 NewScore)
    {
        if (NewScore != PlayerScore)
        {
            UE_MVVM_SET_PROPERTY_VALUE(PlayerScore, NewScore);
            UE_LOG(LogTemp, Log, TEXT("【MVVM数据】玩家分数更新: %d"), NewScore);

            // 【性能优化】触发相关计算属性更新
            OnPlayerScoreChanged();
        }
    }

    UFUNCTION(BlueprintCallable, Category = "Player Data")
    void UpdatePlayerHealth(float NewHealth)
    {
        const float ClampedHealth = FMath::Clamp(NewHealth, 0.0f, 100.0f);
        if (!FMath::IsNearlyEqual(ClampedHealth, PlayerHealth))
        {
            UE_MVVM_SET_PROPERTY_VALUE(PlayerHealth, ClampedHealth);
            UE_LOG(LogTemp, VeryVerbose, TEXT("【MVVM数据】玩家血量更新: %.1f"), ClampedHealth);

            // 【事件驱动】触发血量变化事件
            OnPlayerHealthChanged();
        }
    }

    UFUNCTION(BlueprintCallable, Category = "Game State")
    void SetGamePaused(bool bPaused)
    {
        if (bPaused != bIsGamePaused)
        {
            UE_MVVM_SET_PROPERTY_VALUE(bIsGamePaused, bPaused);
            UE_LOG(LogTemp, Log, TEXT("【MVVM状态】游戏暂停状态: %s"), bPaused ? TEXT("暂停") : TEXT("继续"));
        }
    }

    // 【计算属性】基于现有数据的派生属性
    UFUNCTION(BlueprintPure, Category = "Player Data")
    FText GetPlayerScoreText() const
    {
        return FText::Format(NSLOCTEXT("Game", "ScoreFormat", "Score: {0}"), PlayerScore);
    }

    UFUNCTION(BlueprintPure, Category = "Player Data")
    float GetHealthPercentage() const
    {
        return PlayerHealth / 100.0f;
    }

    UFUNCTION(BlueprintPure, Category = "Player Data")
    bool IsPlayerAlive() const
    {
        return PlayerHealth > 0.0f;
    }

protected:
    // 【事件处理】属性变化回调
    virtual void OnPlayerScoreChanged()
    {
        // 可在子类中重写以处理分数变化逻辑
        if (PlayerScore > 0 && PlayerScore % 1000 == 0)
        {
            UE_LOG(LogTemp, Log, TEXT("【成就系统】玩家达到里程碑分数: %d"), PlayerScore);
        }
    }

    virtual void OnPlayerHealthChanged()
    {
        // 可在子类中重写以处理血量变化逻辑
        if (PlayerHealth <= 20.0f && PlayerHealth > 0.0f)
        {
            UE_LOG(LogTemp, Warning, TEXT("【警告系统】玩家血量危险: %.1f"), PlayerHealth);
        }
    }
};

// 专门用于Mass Entity数据的ViewModel
UCLASS(BlueprintType)
class GAME_API UMassEntityViewModel : public UMVVMViewModelBase
{
    GENERATED_BODY()

public:
    // 【Mass Entity集成】实体数据绑定
    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Mass Entity")
    int32 EntityCount = 0;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Mass Entity")
    float AveragePerformanceLevel = 1.0f;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Mass Entity")
    int32 ActiveProcessorCount = 0;

    // 【性能监控】更新Mass Entity统计数据
    UFUNCTION(BlueprintCallable, Category = "Mass Entity")
    void UpdateMassEntityStats(int32 NewEntityCount, float NewPerformanceLevel, int32 NewProcessorCount)
    {
        bool bNeedsUpdate = false;

        if (NewEntityCount != EntityCount)
        {
            UE_MVVM_SET_PROPERTY_VALUE(EntityCount, NewEntityCount);
            bNeedsUpdate = true;
        }

        if (!FMath::IsNearlyEqual(NewPerformanceLevel, AveragePerformanceLevel))
        {
            UE_MVVM_SET_PROPERTY_VALUE(AveragePerformanceLevel, NewPerformanceLevel);
            bNeedsUpdate = true;
        }

        if (NewProcessorCount != ActiveProcessorCount)
        {
            UE_MVVM_SET_PROPERTY_VALUE(ActiveProcessorCount, NewProcessorCount);
            bNeedsUpdate = true;
        }

        if (bNeedsUpdate)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity MVVM】统计数据更新: 实体数=%d, 性能=%.2f, 处理器数=%d"),
                   EntityCount, AveragePerformanceLevel, ActiveProcessorCount);
        }
    }

    // 【计算属性】Mass Entity性能指标
    UFUNCTION(BlueprintPure, Category = "Mass Entity")
    FText GetEntityCountText() const
    {
        return FText::Format(NSLOCTEXT("Game", "EntityCountFormat", "Entities: {0}"), EntityCount);
    }

    UFUNCTION(BlueprintPure, Category = "Mass Entity")
    FLinearColor GetPerformanceColor() const
    {
        // 根据性能级别返回颜色
        if (AveragePerformanceLevel >= 0.8f)
        {
            return FLinearColor::Green;
        }
        else if (AveragePerformanceLevel >= 0.5f)
        {
            return FLinearColor::Yellow;
        }
        else
        {
            return FLinearColor::Red;
        }
    }
};
```

#### ✅ View层标准实现（UMG集成）
```cpp
// 基于UMG的MVVM View实现
UCLASS(BlueprintType, Blueprintable)
class GAME_API UGameHUDWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    virtual void NativeConstruct() override
    {
        Super::NativeConstruct();
        SetupMVVMBindings();
        UE_LOG(LogTemp, Log, TEXT("【MVVM View】游戏HUD界面初始化完成"));
    }

protected:
    // 【MVVM绑定】ViewModel引用
    UPROPERTY(BlueprintReadOnly, Category = "MVVM", meta = (BindWidget))
    TObjectPtr<UGameDataViewModel> GameDataViewModel;

    // 【UMG组件】UI控件引用
    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> ScoreText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UProgressBar> HealthBar;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> PlayerNameText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UButton> PauseButton;

    // 【MVVM设置】建立数据绑定
    void SetupMVVMBindings()
    {
        if (!IsValid(GameDataViewModel))
        {
            // 【对象创建】创建ViewModel实例
            GameDataViewModel = NewObject<UGameDataViewModel>(this);
        }

        // 【UE5.6 MVVM】使用UMVVMSubsystem建立绑定
        if (UMVVMSubsystem* MVVMSubsystem = GEngine->GetEngineSubsystem<UMVVMSubsystem>())
        {
            if (UMVVMView* MVVMView = MVVMSubsystem->GetViewFromUserWidget(this))
            {
                // 建立ViewModel到View的绑定
                MVVMView->SetViewModel(FMVVMViewModelContext(GameDataViewModel));
                UE_LOG(LogTemp, Log, TEXT("【MVVM绑定】ViewModel绑定成功"));
            }
        }

        // 【事件绑定】UI事件处理
        if (IsValid(PauseButton))
        {
            PauseButton->OnClicked.AddDynamic(this, &UGameHUDWidget::OnPauseButtonClicked);
        }
    }

    // 【UI事件处理】暂停按钮点击
    UFUNCTION()
    void OnPauseButtonClicked()
    {
        if (IsValid(GameDataViewModel))
        {
            const bool bNewPausedState = !GameDataViewModel->bIsGamePaused;
            GameDataViewModel->SetGamePaused(bNewPausedState);
            UE_LOG(LogTemp, Log, TEXT("【UI交互】暂停按钮点击，新状态: %s"),
                   bNewPausedState ? TEXT("暂停") : TEXT("继续"));
        }
    }

    // 【蓝图接口】供蓝图调用的ViewModel访问
    UFUNCTION(BlueprintPure, Category = "MVVM")
    UGameDataViewModel* GetGameDataViewModel() const
    {
        return GameDataViewModel;
    }

    // 【数据更新】手动更新UI（用于非绑定场景）
    UFUNCTION(BlueprintCallable, Category = "UI")
    void RefreshUIData()
    {
        if (!IsValid(GameDataViewModel))
        {
            return;
        }

        // 【性能优化】只在数据变化时更新UI
        if (IsValid(ScoreText))
        {
            ScoreText->SetText(GameDataViewModel->GetPlayerScoreText());
        }

        if (IsValid(HealthBar))
        {
            HealthBar->SetPercent(GameDataViewModel->GetHealthPercentage());
        }

        if (IsValid(PlayerNameText))
        {
            PlayerNameText->SetText(FText::FromString(GameDataViewModel->PlayerName));
        }
    }
};
```

### 🏗️ MVC模式游戏逻辑分层规范

#### ✅ Model层实现（数据和业务逻辑）
```cpp
// 游戏数据模型基类
UCLASS(BlueprintType, Abstract)
class GAME_API UGameModel : public UObject
{
    GENERATED_BODY()

public:
    // 【数据驱动】从DataAsset加载配置
    virtual void LoadFromDataAsset(const UDataAsset* ConfigAsset) PURE_VIRTUAL(UGameModel::LoadFromDataAsset,);

    // 【数据验证】验证模型数据完整性
    virtual bool ValidateData() const PURE_VIRTUAL(UGameModel::ValidateData, return false;);

    // 【序列化支持】保存和加载数据
    virtual void SaveToArchive(FArchive& Archive) PURE_VIRTUAL(UGameModel::SaveToArchive,);
    virtual void LoadFromArchive(FArchive& Archive) PURE_VIRTUAL(UGameModel::LoadFromArchive,);

protected:
    // 【事件通知】数据变化通知
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnModelDataChanged, UGameModel*);
    FOnModelDataChanged OnModelDataChanged;

public:
    // 【事件访问】获取数据变化事件
    FOnModelDataChanged& GetDataChangedEvent() { return OnModelDataChanged; }
};

// 玩家数据模型实现
UCLASS(BlueprintType)
class GAME_API UPlayerDataModel : public UGameModel
{
    GENERATED_BODY()

public:
    // 【核心数据】玩家基础属性
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    int32 Score = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    float Health = 100.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    int32 Level = 1;

    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    FString PlayerName = TEXT("Player");

    // 【业务逻辑】分数计算
    UFUNCTION(BlueprintCallable, Category = "Business Logic")
    void AddScore(int32 Points, const FString& Reason = TEXT(""))
    {
        const int32 OldScore = Score;
        Score = FMath::Max(0, Score + Points);

        if (Score != OldScore)
        {
            UE_LOG(LogTemp, Log, TEXT("【业务逻辑】分数变化: %d -> %d, 原因: %s"),
                   OldScore, Score, *Reason);

            // 【等级计算】检查是否升级
            CheckLevelUp();

            // 【事件通知】通知数据变化
            OnModelDataChanged.Broadcast(this);
        }
    }

    // 【业务逻辑】血量管理
    UFUNCTION(BlueprintCallable, Category = "Business Logic")
    void TakeDamage(float Damage, const FString& Source = TEXT(""))
    {
        const float OldHealth = Health;
        Health = FMath::Clamp(Health - Damage, 0.0f, 100.0f);

        if (!FMath::IsNearlyEqual(Health, OldHealth))
        {
            UE_LOG(LogTemp, Log, TEXT("【业务逻辑】血量变化: %.1f -> %.1f, 来源: %s"),
                   OldHealth, Health, *Source);

            // 【死亡检测】检查玩家是否死亡
            if (Health <= 0.0f && OldHealth > 0.0f)
            {
                OnPlayerDeath();
            }

            OnModelDataChanged.Broadcast(this);
        }
    }

    // 【数据查询】获取玩家状态
    UFUNCTION(BlueprintPure, Category = "Player Data")
    bool IsAlive() const { return Health > 0.0f; }

    UFUNCTION(BlueprintPure, Category = "Player Data")
    float GetHealthPercentage() const { return Health / 100.0f; }

    UFUNCTION(BlueprintPure, Category = "Player Data")
    int32 GetRequiredScoreForNextLevel() const { return Level * 1000; }

    // 【UGameModel接口实现】
    virtual void LoadFromDataAsset(const UDataAsset* ConfigAsset) override
    {
        // 从配置资产加载初始数据
        if (const UPlayerConfigDataAsset* PlayerConfig = Cast<UPlayerConfigDataAsset>(ConfigAsset))
        {
            Health = PlayerConfig->InitialHealth;
            PlayerName = PlayerConfig->DefaultPlayerName;
            UE_LOG(LogTemp, Log, TEXT("【数据加载】玩家配置加载完成"));
        }
    }

    virtual bool ValidateData() const override
    {
        return Health >= 0.0f && Health <= 100.0f &&
               Score >= 0 && Level >= 1 &&
               !PlayerName.IsEmpty();
    }

protected:
    // 【业务逻辑】等级检查
    void CheckLevelUp()
    {
        const int32 RequiredScore = GetRequiredScoreForNextLevel();
        if (Score >= RequiredScore)
        {
            Level++;
            UE_LOG(LogTemp, Log, TEXT("【等级系统】玩家升级到等级: %d"), Level);
        }
    }

    // 【事件处理】玩家死亡
    void OnPlayerDeath()
    {
        UE_LOG(LogTemp, Warning, TEXT("【游戏事件】玩家死亡"));
        // 触发死亡相关逻辑
    }
};
```

#### ✅ Controller层实现（控制逻辑）
```cpp
// 游戏控制器基类
UCLASS(BlueprintType, Abstract)
class GAME_API UGameController : public UObject
{
    GENERATED_BODY()

public:
    // 【初始化】控制器初始化
    virtual void Initialize(UGameModel* InModel) PURE_VIRTUAL(UGameController::Initialize,);

    // 【生命周期】控制器更新
    virtual void Tick(float DeltaTime) {}

    // 【清理】控制器清理
    virtual void Cleanup() {}

protected:
    // 【模型引用】关联的数据模型
    UPROPERTY(BlueprintReadOnly, Category = "MVC")
    TObjectPtr<UGameModel> Model;

    // 【事件处理】模型数据变化处理
    UFUNCTION()
    virtual void OnModelDataChanged(UGameModel* ChangedModel) {}
};

// 玩家控制器实现
UCLASS(BlueprintType)
class GAME_API UPlayerController_MVC : public UGameController
{
    GENERATED_BODY()

public:
    virtual void Initialize(UGameModel* InModel) override
    {
        Model = InModel;
        PlayerModel = Cast<UPlayerDataModel>(InModel);

        if (IsValid(PlayerModel))
        {
            // 【事件绑定】监听模型数据变化
            PlayerModel->GetDataChangedEvent().AddUObject(this, &UPlayerController_MVC::OnModelDataChanged);
            UE_LOG(LogTemp, Log, TEXT("【MVC控制器】玩家控制器初始化完成"));
        }
    }

    // 【输入处理】处理玩家输入
    UFUNCTION(BlueprintCallable, Category = "Player Control")
    void HandleScoreInput(int32 ScoreToAdd)
    {
        if (IsValid(PlayerModel))
        {
            PlayerModel->AddScore(ScoreToAdd, TEXT("玩家输入"));
        }
    }

    UFUNCTION(BlueprintCallable, Category = "Player Control")
    void HandleDamageInput(float Damage)
    {
        if (IsValid(PlayerModel))
        {
            PlayerModel->TakeDamage(Damage, TEXT("环境伤害"));
        }
    }

    // 【命令模式集成】执行玩家命令
    UFUNCTION(BlueprintCallable, Category = "Player Control")
    void ExecutePlayerCommand(TSubclassOf<UMassCommand> CommandClass)
    {
        if (UCommandPoolManager* CommandPool = GetWorld()->GetSubsystem<UCommandPoolManager>())
        {
            if (TObjectPtr<UMassCommand> Command = CommandPool->GetPooledCommand(CommandClass))
            {
                // 【MVC集成】将模型数据传递给命令
                if (UPlayerCommand* PlayerCommand = Cast<UPlayerCommand>(Command))
                {
                    PlayerCommand->SetPlayerData(PlayerModel);
                }

                // 【命令队列】将命令加入执行队列
                if (UMassCommandProcessor* CommandProcessor = GetWorld()->GetSubsystem<UMassCommandProcessor>())
                {
                    CommandProcessor->AddCommand(Command);
                }
            }
        }
    }

    virtual void Tick(float DeltaTime) override
    {
        Super::Tick(DeltaTime);

        // 【自动逻辑】处理自动恢复等逻辑
        if (IsValid(PlayerModel) && PlayerModel->IsAlive())
        {
            HandleAutoRegeneration(DeltaTime);
        }
    }

protected:
    // 【类型转换】玩家数据模型引用
    UPROPERTY(BlueprintReadOnly, Category = "MVC")
    TObjectPtr<UPlayerDataModel> PlayerModel;

    // 【自动恢复】血量自动恢复逻辑
    void HandleAutoRegeneration(float DeltaTime)
    {
        static float RegenerationTimer = 0.0f;
        RegenerationTimer += DeltaTime;

        if (RegenerationTimer >= 1.0f) // 每秒恢复
        {
            const float CurrentHealth = PlayerModel->Health;
            if (CurrentHealth < 100.0f && CurrentHealth > 0.0f)
            {
                const float NewHealth = FMath::Min(100.0f, CurrentHealth + 1.0f);
                PlayerModel->Health = NewHealth;
                PlayerModel->GetDataChangedEvent().Broadcast(PlayerModel);
            }
            RegenerationTimer = 0.0f;
        }
    }

    // 【事件处理】模型数据变化响应
    virtual void OnModelDataChanged(UGameModel* ChangedModel) override
    {
        if (UPlayerDataModel* PlayerData = Cast<UPlayerDataModel>(ChangedModel))
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【MVC控制器】玩家数据变化: 分数=%d, 血量=%.1f, 等级=%d"),
                   PlayerData->Score, PlayerData->Health, PlayerData->Level);

            // 【MVVM集成】更新ViewModel
            UpdateViewModel(PlayerData);
        }
    }

    // 【MVVM集成】将Model数据同步到ViewModel
    void UpdateViewModel(UPlayerDataModel* PlayerData)
    {
        // 查找当前活动的ViewModel并更新
        if (UGameDataViewModel* ViewModel = FindActiveViewModel())
        {
            ViewModel->UpdatePlayerScore(PlayerData->Score);
            ViewModel->UpdatePlayerHealth(PlayerData->Health);
            // 其他数据同步...
        }
    }

private:
    UGameDataViewModel* FindActiveViewModel()
    {
        // 实现查找当前活动ViewModel的逻辑
        // 这里简化处理，实际项目中可能需要更复杂的查找逻辑
        return nullptr;
    }
};
```

### 📊 数据绑定最佳实践

#### ✅ 双向数据绑定规范
```cpp
// 数据绑定管理器
UCLASS()
class GAME_API UDataBindingManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【双向绑定】建立Model和ViewModel之间的双向绑定
    template<typename ModelType, typename ViewModelType>
    void CreateTwoWayBinding(ModelType* Model, ViewModelType* ViewModel)
    {
        static_assert(TIsDerivedFrom<ModelType, UGameModel>::IsDerived,
                     "ModelType must derive from UGameModel");
        static_assert(TIsDerivedFrom<ViewModelType, UMVVMViewModelBase>::IsDerived,
                     "ViewModelType must derive from UMVVMViewModelBase");

        if (!IsValid(Model) || !IsValid(ViewModel))
        {
            UE_LOG(LogTemp, Error, TEXT("【数据绑定】无效的Model或ViewModel"));
            return;
        }

        // 【Model到ViewModel】监听Model变化更新ViewModel
        Model->GetDataChangedEvent().AddUObject(this, &UDataBindingManager::OnModelChanged);

        // 【绑定记录】记录绑定关系
        FDataBindingPair BindingPair;
        BindingPair.Model = Model;
        BindingPair.ViewModel = ViewModel;
        ActiveBindings.Add(BindingPair);

        UE_LOG(LogTemp, Log, TEXT("【数据绑定】双向绑定建立成功: %s <-> %s"),
               *Model->GetClass()->GetName(), *ViewModel->GetClass()->GetName());
    }

    // 【性能优化】批量更新绑定
    UFUNCTION(BlueprintCallable, Category = "Data Binding")
    void BatchUpdateBindings()
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(DataBindingManager_BatchUpdate);

        for (const FDataBindingPair& Binding : ActiveBindings)
        {
            if (IsValid(Binding.Model) && IsValid(Binding.ViewModel))
            {
                UpdateViewModelFromModel(Binding.Model, Binding.ViewModel);
            }
        }
    }

protected:
    // 绑定关系结构
    USTRUCT()
    struct FDataBindingPair
    {
        GENERATED_BODY()

        UPROPERTY()
        TObjectPtr<UGameModel> Model = nullptr;

        UPROPERTY()
        TObjectPtr<UMVVMViewModelBase> ViewModel = nullptr;
    };

private:
    // 【绑定存储】活动绑定列表
    UPROPERTY()
    TArray<FDataBindingPair> ActiveBindings;

    // 【事件处理】Model变化处理
    UFUNCTION()
    void OnModelChanged(UGameModel* ChangedModel)
    {
        // 查找对应的ViewModel并更新
        for (const FDataBindingPair& Binding : ActiveBindings)
        {
            if (Binding.Model == ChangedModel && IsValid(Binding.ViewModel))
            {
                UpdateViewModelFromModel(ChangedModel, Binding.ViewModel);
                break;
            }
        }
    }

    void UpdateViewModelFromModel(UGameModel* Model, UMVVMViewModelBase* ViewModel)
    {
        // 根据具体类型进行数据同步
        if (UPlayerDataModel* PlayerModel = Cast<UPlayerDataModel>(Model))
        {
            if (UGameDataViewModel* GameViewModel = Cast<UGameDataViewModel>(ViewModel))
            {
                GameViewModel->UpdatePlayerScore(PlayerModel->Score);
                GameViewModel->UpdatePlayerHealth(PlayerModel->Health);
                // 其他属性同步...
            }
        }
    }
};
```

### ❌ 严格禁止的MVC/MVVM实现

#### ❌ 禁止的架构违规模式
```cpp
// 禁止View直接访问Model
class BadView : public UUserWidget
{
    // 错误：View直接引用Model，违反MVC分层原则
    UPROPERTY()
    TObjectPtr<UPlayerDataModel> DirectModelReference; // 违反架构分层

public:
    void BadUpdateUI()
    {
        // 错误：View直接操作Model数据
        DirectModelReference->Score += 100; // 违反职责分离
    }
};

// 禁止Model直接引用View
class BadModel : public UGameModel
{
    // 错误：Model直接引用UI组件
    UPROPERTY()
    TObjectPtr<UUserWidget> UIReference; // 违反依赖方向

public:
    void BadUpdateScore(int32 NewScore)
    {
        Score = NewScore;
        // 错误：Model直接更新UI
        if (IsValid(UIReference))
        {
            // 直接操作UI违反MVC原则
        }
    }
};

// 禁止在ViewModel中包含业务逻辑
class BadViewModel : public UMVVMViewModelBase
{
public:
    // 错误：在ViewModel中实现复杂业务逻辑
    UFUNCTION(BlueprintCallable)
    void BadBusinessLogic()
    {
        // 错误：复杂的游戏规则计算应该在Model中
        if (PlayerLevel > 10 && HasSpecialItem)
        {
            // 复杂的业务逻辑不应该在ViewModel中
            CalculateComplexBonus();
        }
    }

private:
    // 错误：ViewModel不应该直接访问游戏世界
    void CalculateComplexBonus()
    {
        // 错误：访问游戏世界对象
        if (UWorld* World = GetWorld())
        {
            // ViewModel不应该直接操作游戏世界
        }
    }
};

// 禁止循环依赖
class BadController : public UGameController
{
    // 错误：Controller引用View，形成循环依赖
    UPROPERTY()
    TObjectPtr<UUserWidget> ViewReference; // 违反单向依赖原则
};
```

#### ❌ 禁止的数据绑定模式
```cpp
// 禁止手动的字符串绑定
class BadDataBinding
{
    void BadBindingSetup()
    {
        // 错误：使用字符串进行属性绑定，容易出错且难以维护
        BindProperty("PlayerScore", "ScoreText"); // 违反类型安全
    }
};

// 禁止在UI线程外更新ViewModel
class BadAsyncUpdate
{
    void BadAsyncViewModelUpdate()
    {
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []()
        {
            // 错误：在后台线程直接更新ViewModel
            ViewModel->UpdatePlayerScore(NewScore); // 违反线程安全
        });
    }
};

// 禁止ViewModel之间的直接依赖
class BadViewModelDependency : public UMVVMViewModelBase
{
    // 错误：ViewModel直接依赖其他ViewModel
    UPROPERTY()
    TObjectPtr<UGameDataViewModel> OtherViewModel; // 违反ViewModel独立性

public:
    void BadUpdate()
    {
        // 错误：ViewModel之间直接通信
        if (IsValid(OtherViewModel))
        {
            OtherViewModel->UpdatePlayerScore(100); // 应该通过Model或事件系统
        }
    }
};
```

## 架构模式集成示例

### 🎯 综合示例：游戏状态管理系统

#### ✅ 多模式协同架构设计
```cpp
// 【综合示例】游戏状态管理系统 - 集成事件驱动、命令模式、MVVM、Mass Entity
UCLASS()
class GAME_API UGameStateManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【事件驱动】游戏状态变更事件
    DECLARE_MULTICAST_DELEGATE_TwoParams(FOnGameStateChanged, EGameState /*OldState*/, EGameState /*NewState*/);
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerScoreChanged, int32 /*NewScore*/);
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnMassEntityStatsChanged, const FMassEntityStats& /*Stats*/);

    virtual void Initialize(FSubsystemCollectionBase& Collection) override
    {
        Super::Initialize(Collection);
        InitializeArchitectureComponents();
        UE_LOG(LogTemp, Log, TEXT("【架构集成】游戏状态管理系统初始化完成"));
    }

    // 【命令模式】执行状态变更命令
    UFUNCTION(BlueprintCallable, Category = "Game State")
    void ExecuteStateChangeCommand(TSubclassOf<UGameStateCommand> CommandClass, EGameState NewState)
    {
        // 【对象池优化】从池中获取命令对象
        if (UCommandPoolManager* CommandPool = GetWorld()->GetSubsystem<UCommandPoolManager>())
        {
            if (TObjectPtr<UGameStateCommand> Command = CommandPool->GetPooledCommand<UGameStateCommand>())
            {
                // 【命令配置】设置命令参数
                Command->SetTargetState(NewState);
                Command->SetCurrentState(CurrentGameState);
                Command->SetStateManager(this);

                // 【命令队列】加入执行队列
                if (UMassCommandProcessor* CommandProcessor = GetWorld()->GetSubsystem<UMassCommandProcessor>())
                {
                    CommandProcessor->AddCommand(Command);
                    UE_LOG(LogTemp, Log, TEXT("【命令模式】状态变更命令已加入队列: %s -> %s"),
                           *UEnum::GetValueAsString(CurrentGameState), *UEnum::GetValueAsString(NewState));
                }
            }
        }
    }

    // 【MVVM集成】更新ViewModel数据
    void UpdateGameStateViewModel(EGameState NewState, int32 PlayerScore, const FMassEntityStats& EntityStats)
    {
        if (IsValid(GameStateViewModel))
        {
            // 【数据绑定】更新ViewModel属性
            GameStateViewModel->SetGameState(NewState);
            GameStateViewModel->UpdatePlayerScore(PlayerScore);
            GameStateViewModel->UpdateMassEntityStats(EntityStats.EntityCount, EntityStats.AveragePerformance, EntityStats.ProcessorCount);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【MVVM集成】ViewModel数据已更新"));
        }
    }

    // 【Mass Entity集成】处理实体统计数据
    void ProcessMassEntityStats()
    {
        if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
        {
            FMassEntityManager& EntityManager = MassSubsystem->GetMutableEntityManager();

            // 【性能统计】收集Mass Entity统计信息
            FMassEntityStats Stats;
            Stats.EntityCount = EntityManager.GetNumEntities();
            Stats.AveragePerformance = CalculateAveragePerformance(EntityManager);
            Stats.ProcessorCount = GetActiveProcessorCount();

            // 【事件驱动】广播统计数据变更事件
            OnMassEntityStatsChanged.Broadcast(Stats);

            // 【MVVM更新】同步到ViewModel
            UpdateGameStateViewModel(CurrentGameState, CurrentPlayerScore, Stats);
        }
    }

protected:
    // 【当前状态】游戏状态数据
    UPROPERTY(BlueprintReadOnly, Category = "Game State")
    EGameState CurrentGameState = EGameState::MainMenu;

    UPROPERTY(BlueprintReadOnly, Category = "Game State")
    int32 CurrentPlayerScore = 0;

    // 【MVVM组件】ViewModel引用
    UPROPERTY(BlueprintReadOnly, Category = "MVVM")
    TObjectPtr<UGameStateViewModel> GameStateViewModel;

    // 【MVC组件】Model和Controller引用
    UPROPERTY(BlueprintReadOnly, Category = "MVC")
    TObjectPtr<UGameDataModel> GameDataModel;

    UPROPERTY(BlueprintReadOnly, Category = "MVC")
    TObjectPtr<UGameController_MVC> GameController;

    // 【事件处理】状态变更处理
    UFUNCTION()
    void HandleGameStateChanged(EGameState OldState, EGameState NewState)
    {
        CurrentGameState = NewState;

        // 【MVC集成】通知Controller状态变更
        if (IsValid(GameController))
        {
            GameController->OnGameStateChanged(OldState, NewState);
        }

        // 【Performance优化】根据状态调整性能设置
        if (UPerformanceOptimizer* Optimizer = GetWorld()->GetSubsystem<UPerformanceOptimizer>())
        {
            Optimizer->AdjustPerformanceForGameState(NewState);
        }

        UE_LOG(LogTemp, Log, TEXT("【状态管理】游戏状态变更: %s -> %s"),
               *UEnum::GetValueAsString(OldState), *UEnum::GetValueAsString(NewState));
    }

    UFUNCTION()
    void HandlePlayerScoreChanged(int32 NewScore)
    {
        CurrentPlayerScore = NewScore;

        // 【MVC集成】更新Model数据
        if (IsValid(GameDataModel))
        {
            GameDataModel->SetPlayerScore(NewScore);
        }

        // 【MVVM集成】更新ViewModel
        if (IsValid(GameStateViewModel))
        {
            GameStateViewModel->UpdatePlayerScore(NewScore);
        }
    }

private:
    // 【架构初始化】初始化所有架构组件
    void InitializeArchitectureComponents()
    {
        // 【MVVM初始化】创建ViewModel
        GameStateViewModel = NewObject<UGameStateViewModel>(this);

        // 【MVC初始化】创建Model和Controller
        GameDataModel = NewObject<UGameDataModel>(this);
        GameController = NewObject<UGameController_MVC>(this);
        GameController->Initialize(GameDataModel);

        // 【事件绑定】绑定事件处理器
        OnGameStateChanged.AddUObject(this, &UGameStateManager::HandleGameStateChanged);
        OnPlayerScoreChanged.AddUObject(this, &UGameStateManager::HandlePlayerScoreChanged);

        // 【数据绑定】建立Model和ViewModel之间的绑定
        if (UDataBindingManager* BindingManager = GetWorld()->GetSubsystem<UDataBindingManager>())
        {
            BindingManager->CreateTwoWayBinding(GameDataModel, GameStateViewModel);
        }
    }

    // 【性能计算】计算平均性能
    float CalculateAveragePerformance(FMassEntityManager& EntityManager)
    {
        // 简化的性能计算逻辑
        const int32 EntityCount = EntityManager.GetNumEntities();
        return EntityCount > 0 ? FMath::Clamp(1000.0f / EntityCount, 0.1f, 1.0f) : 1.0f;
    }

    int32 GetActiveProcessorCount()
    {
        // 简化的处理器计数逻辑
        return 5; // 假设有5个活动处理器
    }

public:
    // 【事件访问器】公开事件委托
    FOnGameStateChanged OnGameStateChanged;
    FOnPlayerScoreChanged OnPlayerScoreChanged;
    FOnMassEntityStatsChanged OnMassEntityStatsChanged;
};

// 【数据结构】Mass Entity统计信息
USTRUCT(BlueprintType)
struct GAME_API FMassEntityStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Mass Entity")
    int32 EntityCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Mass Entity")
    float AveragePerformance = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Mass Entity")
    int32 ProcessorCount = 0;
};

// 【枚举定义】游戏状态
UENUM(BlueprintType)
enum class EGameState : uint8
{
    MainMenu        UMETA(DisplayName = "Main Menu"),
    Loading         UMETA(DisplayName = "Loading"),
    InGame          UMETA(DisplayName = "In Game"),
    Paused          UMETA(DisplayName = "Paused"),
    GameOver        UMETA(DisplayName = "Game Over")
};
```

#### ✅ 游戏状态变更命令实现
```cpp
// 【命令模式】游戏状态变更命令
UCLASS()
class GAME_API UGameStateCommand : public UMassCommand
{
    GENERATED_BODY()

public:
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        if (!IsValid(StateManager))
        {
            UE_LOG(LogTemp, Error, TEXT("【命令执行】StateManager无效"));
            return;
        }

        const double StartTime = FPlatformTime::Seconds();

        // 【状态验证】检查状态转换是否有效
        if (!IsValidStateTransition(CurrentState, TargetState))
        {
            UE_LOG(LogTemp, Warning, TEXT("【命令执行】无效的状态转换: %s -> %s"),
                   *UEnum::GetValueAsString(CurrentState), *UEnum::GetValueAsString(TargetState));
            return;
        }

        // 【事件驱动】广播状态变更事件
        StateManager->OnGameStateChanged.Broadcast(CurrentState, TargetState);

        // 【Mass Entity集成】根据状态调整Mass Entity处理
        AdjustMassEntityProcessing(EntityManager, Context, TargetState);

        // 【Performance优化】记录执行时间
        const double EndTime = FPlatformTime::Seconds();
        TrackCommandPerformance(StartTime, EndTime);

        UE_LOG(LogTemp, Log, TEXT("【命令执行】状态变更命令执行完成: %s -> %s"),
               *UEnum::GetValueAsString(CurrentState), *UEnum::GetValueAsString(TargetState));
    }

    virtual void Undo(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        if (IsValid(StateManager))
        {
            // 【撤销操作】恢复到之前的状态
            StateManager->OnGameStateChanged.Broadcast(TargetState, CurrentState);
            AdjustMassEntityProcessing(EntityManager, Context, CurrentState);

            UE_LOG(LogTemp, Log, TEXT("【命令撤销】状态变更已撤销: %s -> %s"),
                   *UEnum::GetValueAsString(TargetState), *UEnum::GetValueAsString(CurrentState));
        }
    }

    virtual bool CanUndo() const override { return true; }
    virtual bool CanBatch() const override { return false; } // 状态变更不能批处理

    // 【命令配置】设置命令参数
    void SetTargetState(EGameState InTargetState) { TargetState = InTargetState; }
    void SetCurrentState(EGameState InCurrentState) { CurrentState = InCurrentState; }
    void SetStateManager(UGameStateManager* InStateManager) { StateManager = InStateManager; }

protected:
    virtual FString GetCommandName() const override
    {
        return FString::Printf(TEXT("GameStateCommand_%s_to_%s"),
                              *UEnum::GetValueAsString(CurrentState),
                              *UEnum::GetValueAsString(TargetState));
    }

private:
    UPROPERTY()
    EGameState CurrentState = EGameState::MainMenu;

    UPROPERTY()
    EGameState TargetState = EGameState::MainMenu;

    UPROPERTY()
    TObjectPtr<UGameStateManager> StateManager = nullptr;

    // 【业务逻辑】验证状态转换
    bool IsValidStateTransition(EGameState From, EGameState To) const
    {
        // 定义有效的状态转换规则
        switch (From)
        {
            case EGameState::MainMenu:
                return To == EGameState::Loading;
            case EGameState::Loading:
                return To == EGameState::InGame;
            case EGameState::InGame:
                return To == EGameState::Paused || To == EGameState::GameOver;
            case EGameState::Paused:
                return To == EGameState::InGame || To == EGameState::MainMenu;
            case EGameState::GameOver:
                return To == EGameState::MainMenu;
            default:
                return false;
        }
    }

    // 【Mass Entity集成】根据状态调整实体处理
    void AdjustMassEntityProcessing(FMassEntityManager& EntityManager, FMassExecutionContext& Context, EGameState NewState)
    {
        switch (NewState)
        {
            case EGameState::InGame:
                // 激活所有游戏相关的Mass Processor
                EnableGameplayProcessors(EntityManager);
                break;
            case EGameState::Paused:
                // 暂停非关键的Mass Processor
                PauseNonCriticalProcessors(EntityManager);
                break;
            case EGameState::MainMenu:
                // 禁用游戏相关的Mass Processor
                DisableGameplayProcessors(EntityManager);
                break;
            default:
                break;
        }
    }

    void EnableGameplayProcessors(FMassEntityManager& EntityManager)
    {
        // 实现激活游戏处理器的逻辑
        UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity】激活游戏处理器"));
    }

    void PauseNonCriticalProcessors(FMassEntityManager& EntityManager)
    {
        // 实现暂停非关键处理器的逻辑
        UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity】暂停非关键处理器"));
    }

    void DisableGameplayProcessors(FMassEntityManager& EntityManager)
    {
        // 实现禁用游戏处理器的逻辑
        UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity】禁用游戏处理器"));
    }
};
```

#### ✅ UI集成示例：游戏状态HUD
```cpp
// 【MVVM+UMG集成】游戏状态HUD界面
UCLASS(BlueprintType, Blueprintable)
class GAME_API UGameStateHUDWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    virtual void NativeConstruct() override
    {
        Super::NativeConstruct();
        SetupArchitectureIntegration();
        UE_LOG(LogTemp, Log, TEXT("【UI集成】游戏状态HUD初始化完成"));
    }

protected:
    // 【MVVM绑定】ViewModel引用
    UPROPERTY(BlueprintReadOnly, Category = "MVVM", meta = (BindWidget))
    TObjectPtr<UGameStateViewModel> GameStateViewModel;

    // 【UI组件】界面控件
    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> GameStateText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> PlayerScoreText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> EntityCountText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UProgressBar> PerformanceBar;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UButton> PauseButton;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UButton> MenuButton;

    // 【架构集成】设置架构组件集成
    void SetupArchitectureIntegration()
    {
        // 【MVVM设置】建立ViewModel绑定
        if (!IsValid(GameStateViewModel))
        {
            GameStateViewModel = NewObject<UGameStateViewModel>(this);
        }

        // 【事件驱动】绑定游戏状态管理器事件
        if (UGameStateManager* StateManager = GetWorld()->GetSubsystem<UGameStateManager>())
        {
            StateManager->OnGameStateChanged.AddUObject(this, &UGameStateHUDWidget::OnGameStateChanged);
            StateManager->OnPlayerScoreChanged.AddUObject(this, &UGameStateHUDWidget::OnPlayerScoreChanged);
            StateManager->OnMassEntityStatsChanged.AddUObject(this, &UGameStateHUDWidget::OnMassEntityStatsChanged);
        }

        // 【UI事件绑定】绑定按钮事件
        if (IsValid(PauseButton))
        {
            PauseButton->OnClicked.AddDynamic(this, &UGameStateHUDWidget::OnPauseButtonClicked);
        }

        if (IsValid(MenuButton))
        {
            MenuButton->OnClicked.AddDynamic(this, &UGameStateHUDWidget::OnMenuButtonClicked);
        }

        // 【MVVM绑定】使用UE5.6 MVVM系统
        if (UMVVMSubsystem* MVVMSubsystem = GEngine->GetEngineSubsystem<UMVVMSubsystem>())
        {
            if (UMVVMView* MVVMView = MVVMSubsystem->GetViewFromUserWidget(this))
            {
                MVVMView->SetViewModel(FMVVMViewModelContext(GameStateViewModel));
            }
        }
    }

    // 【事件处理】游戏状态变更响应
    UFUNCTION()
    void OnGameStateChanged(EGameState OldState, EGameState NewState)
    {
        // 【UI更新】更新状态显示
        if (IsValid(GameStateText))
        {
            FString StateDisplayName = UEnum::GetDisplayValueAsText(NewState).ToString();
            GameStateText->SetText(FText::FromString(StateDisplayName));
        }

        // 【UI逻辑】根据状态调整UI可见性
        UpdateUIVisibilityForState(NewState);

        UE_LOG(LogTemp, VeryVerbose, TEXT("【UI事件】游戏状态UI已更新: %s"),
               *UEnum::GetValueAsString(NewState));
    }

    UFUNCTION()
    void OnPlayerScoreChanged(int32 NewScore)
    {
        if (IsValid(PlayerScoreText))
        {
            PlayerScoreText->SetText(FText::Format(NSLOCTEXT("Game", "ScoreFormat", "Score: {0}"), NewScore));
        }
    }

    UFUNCTION()
    void OnMassEntityStatsChanged(const FMassEntityStats& Stats)
    {
        // 【性能显示】更新Mass Entity统计显示
        if (IsValid(EntityCountText))
        {
            EntityCountText->SetText(FText::Format(NSLOCTEXT("Game", "EntityCountFormat", "Entities: {0}"), Stats.EntityCount));
        }

        if (IsValid(PerformanceBar))
        {
            PerformanceBar->SetPercent(Stats.AveragePerformance);
        }
    }

    // 【命令模式集成】按钮点击处理
    UFUNCTION()
    void OnPauseButtonClicked()
    {
        if (UGameStateManager* StateManager = GetWorld()->GetSubsystem<UGameStateManager>())
        {
            // 【命令执行】通过命令模式执行状态变更
            StateManager->ExecuteStateChangeCommand(UGameStateCommand::StaticClass(), EGameState::Paused);
        }
    }

    UFUNCTION()
    void OnMenuButtonClicked()
    {
        if (UGameStateManager* StateManager = GetWorld()->GetSubsystem<UGameStateManager>())
        {
            StateManager->ExecuteStateChangeCommand(UGameStateCommand::StaticClass(), EGameState::MainMenu);
        }
    }

private:
    // 【UI逻辑】根据状态更新UI可见性
    void UpdateUIVisibilityForState(EGameState NewState)
    {
        switch (NewState)
        {
            case EGameState::InGame:
                SetVisibility(ESlateVisibility::Visible);
                if (IsValid(PauseButton)) PauseButton->SetVisibility(ESlateVisibility::Visible);
                break;
            case EGameState::Paused:
                if (IsValid(PauseButton)) PauseButton->SetVisibility(ESlateVisibility::Collapsed);
                break;
            case EGameState::MainMenu:
                SetVisibility(ESlateVisibility::Collapsed);
                break;
            default:
                break;
        }
    }
};
```

### 🚀 架构模式集成最佳实践

#### ✅ 性能优化集成策略
```cpp
// 【性能优化】架构模式性能监控管理器
UCLASS()
class GAME_API UArchitecturePerformanceManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【统计收集】收集各架构模式的性能统计
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void CollectArchitecturePerformanceStats()
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(ArchitecturePerformanceManager_CollectStats);

        // 【事件驱动性能】统计事件处理性能
        CollectEventDrivenStats();

        // 【命令模式性能】统计命令执行性能
        CollectCommandPatternStats();

        // 【MVVM性能】统计数据绑定性能
        CollectMVVMStats();

        // 【集成性能】统计多模式协同性能
        CollectIntegrationStats();

        // 【性能报告】生成性能报告
        GeneratePerformanceReport();
    }

    // 【优化建议】基于统计数据提供优化建议
    UFUNCTION(BlueprintCallable, Category = "Performance")
    TArray<FString> GetOptimizationRecommendations() const
    {
        TArray<FString> Recommendations;

        // 【事件驱动优化】
        if (EventProcessingTime > 5.0f) // 5ms阈值
        {
            Recommendations.Add(TEXT("考虑使用异步事件处理减少主线程阻塞"));
            Recommendations.Add(TEXT("检查事件处理器中是否有耗时操作"));
        }

        // 【命令模式优化】
        if (CommandExecutionTime > 10.0f) // 10ms阈值
        {
            Recommendations.Add(TEXT("启用命令批处理以提高执行效率"));
            Recommendations.Add(TEXT("考虑使用对象池减少命令对象创建开销"));
        }

        // 【MVVM优化】
        if (DataBindingUpdateTime > 3.0f) // 3ms阈值
        {
            Recommendations.Add(TEXT("减少ViewModel属性更新频率"));
            Recommendations.Add(TEXT("使用批量更新机制优化数据绑定"));
        }

        // 【内存优化】
        if (MemoryUsage > 100.0f) // 100MB阈值
        {
            Recommendations.Add(TEXT("检查对象池配置，避免内存泄漏"));
            Recommendations.Add(TEXT("优化ViewModel和Command对象的生命周期管理"));
        }

        return Recommendations;
    }

private:
    // 【性能指标】各模式性能统计
    float EventProcessingTime = 0.0f;      // 事件处理时间(ms)
    float CommandExecutionTime = 0.0f;     // 命令执行时间(ms)
    float DataBindingUpdateTime = 0.0f;    // 数据绑定更新时间(ms)
    float MemoryUsage = 0.0f;              // 内存使用量(MB)
    int32 ActiveEventHandlers = 0;         // 活动事件处理器数量
    int32 PendingCommands = 0;             // 待处理命令数量
    int32 ActiveViewModels = 0;            // 活动ViewModel数量

    void CollectEventDrivenStats()
    {
        // 收集事件驱动模式的性能统计
        // 实现具体的统计逻辑
    }

    void CollectCommandPatternStats()
    {
        // 收集命令模式的性能统计
        // 实现具体的统计逻辑
    }

    void CollectMVVMStats()
    {
        // 收集MVVM模式的性能统计
        // 实现具体的统计逻辑
    }

    void CollectIntegrationStats()
    {
        // 收集多模式集成的性能统计
        // 实现具体的统计逻辑
    }

    void GeneratePerformanceReport()
    {
        UE_LOG(LogTemp, Log, TEXT("【性能报告】架构模式性能统计:"));
        UE_LOG(LogTemp, Log, TEXT("  事件处理时间: %.2fms"), EventProcessingTime);
        UE_LOG(LogTemp, Log, TEXT("  命令执行时间: %.2fms"), CommandExecutionTime);
        UE_LOG(LogTemp, Log, TEXT("  数据绑定时间: %.2fms"), DataBindingUpdateTime);
        UE_LOG(LogTemp, Log, TEXT("  内存使用量: %.2fMB"), MemoryUsage);
        UE_LOG(LogTemp, Log, TEXT("  活动组件: 事件处理器=%d, 命令=%d, ViewModel=%d"),
               ActiveEventHandlers, PendingCommands, ActiveViewModels);
    }
};
```

#### ✅ 架构模式集成指导原则

##### 🎯 模式选择决策矩阵
```
功能需求 vs 架构模式选择:

┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│   功能需求      │ 事件驱动    │ 命令模式    │   MVVM      │    MVC      │
├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ UI数据绑定      │     ○       │     ×       │     ●       │     ○       │
│ 组件间通信      │     ●       │     ○       │     ×       │     ○       │
│ 复杂操作序列    │     ○       │     ●       │     ×       │     ○       │
│ 撤销重做功能    │     ×       │     ●       │     ×       │     ○       │
│ 实时数据更新    │     ●       │     ○       │     ●       │     ○       │
│ 业务逻辑分层    │     ○       │     ○       │     ×       │     ●       │
│ 单元测试支持    │     ○       │     ●       │     ○       │     ●       │
│ 性能敏感操作    │     ●       │     ●       │     ○       │     ○       │
└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┘

图例: ● 强烈推荐  ○ 适用  × 不推荐
```

##### 🔧 集成实施检查清单

**📋 事件驱动模式集成检查**
- [ ] 使用UE5.6原生委托系统 (DECLARE_MULTICAST_DELEGATE)
- [ ] 实现异步事件处理避免主线程阻塞
- [ ] 建立事件生命周期管理机制
- [ ] 集成Performance系统的对象池优化
- [ ] 添加事件处理性能监控

**📋 命令模式集成检查**
- [ ] 基于UMassCommand实现命令基类
- [ ] 集成Mass Entity系统的批处理机制
- [ ] 实现撤销/重做功能支持
- [ ] 使用对象池管理命令对象生命周期
- [ ] 建立命令队列优先级管理

**📋 MVVM模式集成检查**
- [ ] 基于UE5.6 ModelViewViewModel模块实现
- [ ] 使用UE_MVVM_SET_PROPERTY_VALUE宏更新属性
- [ ] 建立ViewModel与UMG的标准绑定
- [ ] 实现数据验证和格式化机制
- [ ] 集成Mass Entity数据的MVVM绑定

**📋 MVC模式集成检查**
- [ ] 实现Model、View、Controller的清晰分层
- [ ] 建立Model数据变化的事件通知机制
- [ ] Controller集成命令模式处理复杂逻辑
- [ ] 实现Model数据的序列化和持久化
- [ ] 建立单元测试友好的架构设计

**📋 多模式协同检查**
- [ ] 事件驱动 + 命令模式的无缝集成
- [ ] MVVM + Mass Entity的数据同步
- [ ] MVC + Performance系统的优化集成
- [ ] 所有模式与ECS架构的兼容性
- [ ] 统一的错误处理和日志记录

##### 🎯 常见集成陷阱及避免方法

**⚠️ 陷阱1: 过度设计**
- **问题**: 为简单功能使用复杂的架构模式组合
- **避免**: 根据功能复杂度选择合适的模式，简单功能使用简单实现

**⚠️ 陷阱2: 循环依赖**
- **问题**: 不同架构模式之间形成循环引用
- **避免**: 明确定义模式间的依赖方向，使用事件解耦

**⚠️ 陷阱3: 性能开销**
- **问题**: 多层架构导致不必要的性能开销
- **避免**: 使用性能监控，在关键路径上优化或简化架构

**⚠️ 陷阱4: 状态不一致**
- **问题**: 多个模式管理相同数据导致状态不同步
- **避免**: 建立单一数据源原则，使用事件驱动同步状态

**⚠️ 陷阱5: 内存泄漏**
- **问题**: 事件绑定和对象引用未正确清理
- **避免**: 实现完整的生命周期管理，使用弱引用避免循环引用

這個規範文件現在為AI Agent提供了完整的UE5.6項目開發指導，包含了具體的代碼示例、禁止事項、決策規則和文件交互規範。所有規則都使用命令式語言，專注於為AI提供明確的操作指導。
